package com.concise.gen.signatureauthorization.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.util.ElectronicSignatureUtil;
import com.concise.common.util.PdfSignUtil;
import com.concise.gen.signatureauthorization.entity.SignatureAuthorization;
import com.concise.gen.signatureauthorization.enums.SignatureAuthorizationExceptionEnum;
import com.concise.gen.signatureauthorization.mapper.SignatureAuthorizationMapper;
import com.concise.gen.signatureauthorization.param.SignatureAuthorizationParam;
import com.concise.gen.signatureauthorization.service.SignatureAuthorizationService;
import com.concise.gen.signaturemaintenance.entity.SignatureMaintenance;
import com.concise.gen.signaturemaintenance.mapper.SignatureMaintenanceMapper;
import com.itextpdf.kernel.geom.Rectangle;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 签章授权表service接口实现类
 *
 * <AUTHOR>
 * @date 2025-07-31 11:04:05
 */
@Service
public class SignatureAuthorizationServiceImpl extends ServiceImpl<SignatureAuthorizationMapper, SignatureAuthorization> implements SignatureAuthorizationService {

    @Resource
    private SysFileInfoService sysFileInfoService;

    @Resource
    private SignatureMaintenanceMapper signatureMaintenanceMapper;

    @Resource
    private ElectronicSignatureUtil electronicSignatureUtil;

    @Override
    public PageResult<SignatureAuthorization> page(SignatureAuthorizationParam signatureAuthorizationParam) {
        QueryWrapper<SignatureAuthorization> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(signatureAuthorizationParam)) {

            // 根据印章id 查询
            if (ObjectUtil.isNotEmpty(signatureAuthorizationParam.getSignatureId())) {
                queryWrapper.lambda().eq(SignatureAuthorization::getSignatureId, signatureAuthorizationParam.getSignatureId());
            }
            // 根据授权使用人员id 查询
            if (ObjectUtil.isNotEmpty(signatureAuthorizationParam.getUserIds())) {
                queryWrapper.lambda().eq(SignatureAuthorization::getUserIds, signatureAuthorizationParam.getUserIds());
            }
            // 根据授权使用人员姓名 查询
            if (ObjectUtil.isNotEmpty(signatureAuthorizationParam.getUserNames())) {
                queryWrapper.lambda().eq(SignatureAuthorization::getUserNames, signatureAuthorizationParam.getUserNames());
            }
            // 根据授权开始时间 查询
            if (ObjectUtil.isNotEmpty(signatureAuthorizationParam.getBeginTime())) {
                queryWrapper.lambda().eq(SignatureAuthorization::getBeginTime, signatureAuthorizationParam.getBeginTime());
            }
            // 根据授权结束时间 查询
            if (ObjectUtil.isNotEmpty(signatureAuthorizationParam.getEndTime())) {
                queryWrapper.lambda().eq(SignatureAuthorization::getEndTime, signatureAuthorizationParam.getEndTime());
            }
            // 根据授权证明方式（1-在线授权，2-线下授权） 查询
            if (ObjectUtil.isNotEmpty(signatureAuthorizationParam.getAuthorizationType())) {
                queryWrapper.lambda().eq(SignatureAuthorization::getAuthorizationType, signatureAuthorizationParam.getAuthorizationType());
            }
            // 根据授权状态（1-使用中；2-已过期） 查询
            if (ObjectUtil.isNotEmpty(signatureAuthorizationParam.getAuthorizationStatus())) {
                queryWrapper.lambda().eq(SignatureAuthorization::getAuthorizationStatus, signatureAuthorizationParam.getAuthorizationStatus());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<SignatureAuthorization> list(SignatureAuthorizationParam signatureAuthorizationParam) {
        return this.list();
    }

    @Override
    public void add(SignatureAuthorizationParam signatureAuthorizationParam) {
        SysFileInfo sysFileInfo = signatureAuthorizationParam.getSysFileInfo();
        if (ObjectUtil.isEmpty(sysFileInfo)) {
            throw new ServiceException(SignatureAuthorizationExceptionEnum.AUTHORIZATION_PROOF_NOT_EXIST);
        }
        SignatureAuthorization signatureAuthorization = new SignatureAuthorization();
        BeanUtil.copyProperties(signatureAuthorizationParam, signatureAuthorization);
        signatureAuthorization.setOperator(signatureAuthorizationParam.getUserName());
        this.save(signatureAuthorization);
        sysFileInfo.setBizId(signatureAuthorization.getId());
        sysFileInfoService.updateById(sysFileInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(SignatureAuthorizationParam signatureAuthorizationParam) {
        this.removeById(signatureAuthorizationParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(SignatureAuthorizationParam signatureAuthorizationParam) {
        if (ObjectUtil.isEmpty(signatureAuthorizationParam.getSysFileInfo())) {
            throw new ServiceException(SignatureAuthorizationExceptionEnum.AUTHORIZATION_PROOF_NOT_EXIST);
        }
        SignatureAuthorization signatureAuthorization = this.querySignatureAuthorization(signatureAuthorizationParam);
        BeanUtil.copyProperties(signatureAuthorizationParam, signatureAuthorization);
        this.updateById(signatureAuthorization);
    }

    @Override
    public SignatureAuthorization detail(SignatureAuthorizationParam signatureAuthorizationParam) {
        return this.querySignatureAuthorization(signatureAuthorizationParam);
    }

    /**
     * 获取签章授权表
     *
     * <AUTHOR>
     * @date 2025-07-31 11:04:05
     */
    private SignatureAuthorization querySignatureAuthorization(SignatureAuthorizationParam signatureAuthorizationParam) {
        SignatureAuthorization signatureAuthorization = this.getById(signatureAuthorizationParam.getId());
        if (ObjectUtil.isNull(signatureAuthorization)) {
            throw new ServiceException(SignatureAuthorizationExceptionEnum.NOT_EXIST);
        }
        return signatureAuthorization;
    }

    @Override
    public List<SignatureAuthorization> listBySignatureId(String signatureId) {
        QueryWrapper<SignatureAuthorization> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SignatureAuthorization::getSignatureId, signatureId);
        queryWrapper.lambda().orderByDesc(SignatureAuthorization::getUpdateTime);
        List<SignatureAuthorization> signatureAuthorizations = this.list(queryWrapper);
        if (ObjectUtil.isNotEmpty(signatureAuthorizations)) {
            signatureAuthorizations.forEach(signatureAuthorization -> {
                SysFileInfo fileInfo = sysFileInfoService.getOne(new QueryWrapper<SysFileInfo>().lambda().eq(SysFileInfo::getBizId, signatureAuthorization.getId()).last("limit 1"));
                if (ObjectUtil.isNotNull(fileInfo)) {
                    signatureAuthorization.setSysFileInfo(fileInfo);
                }

            });
            return signatureAuthorizations;
        }
        return Collections.emptyList();
    }

    @Override
    public boolean hasAuthorization(String signatureId, String userId, String realName) {
        //获取签章完整信息
        SignatureMaintenance signatureMaintenance = signatureMaintenanceMapper.selectById(signatureId);
        if (ObjectUtil.isEmpty(signatureMaintenance)) {
            return false;
        }
        //用户本人使用，无需授权
        if (signatureMaintenance.getSealName().equals(realName)) {
            return true;
        }
        //非用户使用，获取在时间范围内的有效授权信息，判断用户id在不在授权范围
        QueryWrapper<SignatureAuthorization> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SignatureAuthorization::getSignatureId, signatureId);
        queryWrapper.lambda().ge(SignatureAuthorization::getBeginTime, new Date());
        queryWrapper.lambda().le(SignatureAuthorization::getEndTime, new Date());
        List<SignatureAuthorization> signatureAuthorizations = this.list(queryWrapper);
        if (ObjectUtil.isNotEmpty(signatureAuthorizations)) {
            for (SignatureAuthorization signatureAuthorization : signatureAuthorizations) {
                if (signatureAuthorization.getUserIds().contains(userId)) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public SysFileInfo seal(String sealNo, String pdfId) {
        SysFileInfo sysFileInfo = sysFileInfoService.getById(pdfId);
        try {
            byte[] pdfBytes = getFileBytes(sysFileInfo);
            List<PdfSignUtil.TextLocation> located = PdfSignUtil.locateTextInPdf(pdfBytes, "盖章");
            if (located.isEmpty()) {
                throw new ServiceException(500, "PDF文件中未找到盖章文本");
            }
            PdfSignUtil.TextLocation textLocation = located.get(0);
            int pageNumber = textLocation.getPageNumber();
            Rectangle rectangle = textLocation.getRectangle();
            int x = (int) rectangle.getX();
            int y = (int) rectangle.getY();
            //字节转base64
            String base64 = Base64.getEncoder().encodeToString(pdfBytes);
            String signeded = electronicSignatureUtil.signedBase64Static(base64, sealNo, sysFileInfo.getFileOriginName(), "1", String.valueOf(x), String.valueOf(y), String.valueOf(pageNumber));
            if (ObjectUtil.isNotEmpty(signeded)) {
                //存放到sysfileinfo
                // 创建MultipartFile对象
                String fileName = sysFileInfo.getFileOriginName();
                MultipartFile multipartFile = new MockMultipartFile(fileName, fileName, "application/pdf", Base64.getDecoder().decode(signeded));
                // 通过SysFileInfoService上传文件
                return sysFileInfoService.uploadFileOss(multipartFile, "pdf", sysFileInfo.getSignType());
            }


        } catch (IOException e) {
            throw new ServiceException(500, e.getMessage());
        }

        return null;
    }

    private byte[] getFileBytes(SysFileInfo sysFileInfo) throws IOException {
        // 读取文件内容
        try (InputStream inputStream = sysFileInfoService.download(sysFileInfo)) {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            return outputStream.toByteArray();
        }
    }
}
