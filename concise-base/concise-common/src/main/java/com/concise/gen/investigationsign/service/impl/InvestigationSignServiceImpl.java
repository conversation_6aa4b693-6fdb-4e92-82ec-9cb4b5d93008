package com.concise.gen.investigationsign.service.impl;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.IOUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.enums.SysFileInfoExceptionEnum;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.file.util.OssSignedUrlUtil;
import com.concise.common.pojo.PdfSignatureParams;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.util.ElectronicSignatureUtil;
import com.concise.common.util.PdfSignUtil;
import com.concise.gen.investigation.entity.InvestigationTranscript;
import com.concise.gen.investigation.param.InvestigationTranscriptParam;
import com.concise.gen.investigation.service.InvestigationTranscriptService;
import com.concise.gen.investigationsign.entity.InvestigationSign;
import com.concise.gen.investigationsign.enums.InvestigationSignExceptionEnum;
import com.concise.gen.investigationsign.mapper.InvestigationSignMapper;
import com.concise.gen.investigationsign.param.InvestigationSignParam;
import com.concise.gen.investigationsign.service.InvestigationSignService;
import com.concise.gen.investigationsigncommonuser.param.InvestigationSignCommonUserParam;
import com.concise.gen.investigationsigncommonuser.service.InvestigationSignCommonUserService;
import com.concise.gen.signaturemaintenance.service.SignatureMaintenanceService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 调查评估_手签service接口实现类
 *
 * <AUTHOR>
 * @date 2025-04-21 20:30:30
 */
@Slf4j
@Service
public class InvestigationSignServiceImpl extends ServiceImpl<InvestigationSignMapper, InvestigationSign> implements InvestigationSignService {

    @Value("${spring.profiles.active}")
    private String env;
    @Value("${zwdd.url}")
    private String url;
    @Value("${zwdd.appKey}")
    private String appKey;
    @Value("${zwdd.appSecret}")
    private String appSecret;
    @Value("${zwdd.send}")
    private boolean send;

    @Resource
    private InvestigationSignCommonUserService investigationSignCommonUserService;

    @Resource
    private SysFileInfoService sysFileInfoService;

    @Resource
    private SignatureMaintenanceService signatureMaintenanceService;

    @Resource
    private ElectronicSignatureUtil electronicSignatureUtil;

    @Resource
    private InvestigationTranscriptService investigationTranscriptService;

    @Override
    public PageResult<InvestigationSign> page(InvestigationSignParam investigationSignParam) {
        QueryWrapper<InvestigationSign> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(investigationSignParam)) {

            // 根据调查评估id 查询
            if (ObjectUtil.isNotEmpty(investigationSignParam.getPid())) {
                queryWrapper.lambda().eq(InvestigationSign::getPid, investigationSignParam.getPid());
            }
            // 根据用户id 查询
            if (ObjectUtil.isNotEmpty(investigationSignParam.getReceiveUserId())) {
                queryWrapper.lambda().eq(InvestigationSign::getReceiveUserId, investigationSignParam.getReceiveUserId());
            }
            // 根据用户类型（1-浙政钉用户；2-浙里办用户） 查询
            if (ObjectUtil.isNotEmpty(investigationSignParam.getReceiveUserType())) {
                queryWrapper.lambda().eq(InvestigationSign::getReceiveUserType, investigationSignParam.getReceiveUserType());
            }
            // 根据用户姓名 查询
            if (ObjectUtil.isNotEmpty(investigationSignParam.getReceiveUserName())) {
                queryWrapper.lambda().eq(InvestigationSign::getReceiveUserName, investigationSignParam.getReceiveUserName());
            }
            // 根据用户手机号 查询
            if (ObjectUtil.isNotEmpty(investigationSignParam.getPhone())) {
                queryWrapper.lambda().eq(InvestigationSign::getPhone, investigationSignParam.getPhone());
            }
            // 根据文书id 查询
            if (ObjectUtil.isNotEmpty(investigationSignParam.getPaperId())) {
                queryWrapper.lambda().eq(InvestigationSign::getPaperId, investigationSignParam.getPaperId());
            }
            // 根据文书标题 查询
            if (ObjectUtil.isNotEmpty(investigationSignParam.getTitle())) {
                queryWrapper.lambda().eq(InvestigationSign::getTitle, investigationSignParam.getTitle());
            }
            // 根据文书类型 查询
            if (ObjectUtil.isNotEmpty(investigationSignParam.getPaperType())) {
                queryWrapper.lambda().eq(InvestigationSign::getPaperType, investigationSignParam.getPaperType());
            }
            // 根据发送单位id 查询
            if (ObjectUtil.isNotEmpty(investigationSignParam.getSendOrgId())) {
                queryWrapper.lambda().eq(InvestigationSign::getSendOrgId, investigationSignParam.getSendOrgId());
            }
            // 根据发送单位 查询
            if (ObjectUtil.isNotEmpty(investigationSignParam.getSendOrgName())) {
                queryWrapper.lambda().eq(InvestigationSign::getSendOrgName, investigationSignParam.getSendOrgName());
            }
            // 根据发送时间 查询
            if (ObjectUtil.isNotEmpty(investigationSignParam.getSendTime())) {
                queryWrapper.lambda().eq(InvestigationSign::getSendTime, investigationSignParam.getSendTime());
            }
            // 根据状态（0-待签；1-已签；2-退回） 查询
            if (ObjectUtil.isNotEmpty(investigationSignParam.getStatus())) {
                queryWrapper.lambda().eq(InvestigationSign::getStatus, investigationSignParam.getStatus());
            }
            // 根据签名时间 查询
            if (ObjectUtil.isNotEmpty(investigationSignParam.getSignTime())) {
                queryWrapper.lambda().eq(InvestigationSign::getSignTime, investigationSignParam.getSignTime());
            }
            // 根据签名图片id 查询
            if (ObjectUtil.isNotEmpty(investigationSignParam.getSignFileBase64())) {
                queryWrapper.lambda().eq(InvestigationSign::getSignFileBase64, investigationSignParam.getSignFileBase64());
            }
            // 根据通知信息 查询
            if (ObjectUtil.isNotEmpty(investigationSignParam.getNoticeInfo())) {
                queryWrapper.lambda().eq(InvestigationSign::getNoticeInfo, investigationSignParam.getNoticeInfo());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<InvestigationSign> list(InvestigationSignParam investigationSignParam) {
        return this.list();
    }

    @Override
    public void add(InvestigationSignParam investigationSignParam) {
        InvestigationSign investigationSign = new InvestigationSign();
        BeanUtil.copyProperties(investigationSignParam, investigationSign);
        this.save(investigationSign);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(InvestigationSignParam investigationSignParam) {
        this.removeById(investigationSignParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(InvestigationSignParam investigationSignParam) {
        InvestigationSign investigationSign = this.queryInvestigationSign(investigationSignParam);
        BeanUtil.copyProperties(investigationSignParam, investigationSign);
        this.updateById(investigationSign);
    }

    @Override
    public InvestigationSign detail(InvestigationSignParam investigationSignParam) {
        return this.queryInvestigationSign(investigationSignParam);
    }

    /**
     * 获取调查评估_手签
     *
     * <AUTHOR>
     * @date 2025-04-21 20:30:30
     */
    private InvestigationSign queryInvestigationSign(InvestigationSignParam investigationSignParam) {
        InvestigationSign investigationSign = this.getById(investigationSignParam.getId());
        if (ObjectUtil.isNull(investigationSign)) {
            throw new ServiceException(InvestigationSignExceptionEnum.NOT_EXIST);
        }
        return investigationSign;
    }

    @Override
    public void addZzDingUser(List<InvestigationSignParam> investigationSignParamList) {
        for (InvestigationSignParam investigationSignParam : investigationSignParamList) {
            InvestigationSign investigationSign = new InvestigationSign();
            BeanUtils.copyProperties(investigationSignParam, investigationSign);
            investigationSign.setReceiveUserType(1);
            investigationSign.setSendTime(DateUtil.date());
            investigationSign.setStatus("0");
            investigationSign.setId(IdWorker.getIdStr());
            if (ObjectUtil.isNotEmpty(investigationSignParam.getPaperType())) {
                investigationSign.setPaperTypeName(exchangePaperTypeName(investigationSignParam.getPaperType()));
            }
            this.save(investigationSign);
//            try {
//                if ("prod".equals(env)) {
//                    ZwddUtils.sendWorkNotificationNormal(investigationSignParam.getReceiveUserId(), "您有一份待签名的文书，可在矫务通或浙里社区矫正专区查看文件详情。", url, appKey, appSecret);
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//                log.info("发送浙政钉通知失败,用户信息:{}", JSON.toJSONString(investigationSignParam));
//            }

        }

    }

    @Override
    public void addZlBUser(List<InvestigationSignParam> investigationSignParamList) {
        for (InvestigationSignParam investigationSignParam : investigationSignParamList) {
            InvestigationSign investigationSign = new InvestigationSign();
            BeanUtils.copyProperties(investigationSignParam, investigationSign);
            if (ObjectUtil.isEmpty(investigationSignParam.getReceiveUserId())) {
                investigationSignParam.setReceiveUserId(IdWorker.getIdStr());
            }
            investigationSign.setId(IdWorker.getIdStr());
            investigationSign.setReceiveUserId(investigationSignParam.getReceiveUserId());
            investigationSign.setReceiveUserType(2);
            investigationSign.setSendTime(DateUtil.date());
            investigationSign.setStatus("0");
            if (ObjectUtil.isNotEmpty(investigationSignParam.getPaperType())) {
                investigationSign.setPaperTypeName(exchangePaperTypeName(investigationSignParam.getPaperType()));
            }
            this.save(investigationSign);
            try {
                //添加常用人员
                InvestigationSignCommonUserParam investigationSignCommonUserParam = new InvestigationSignCommonUserParam();
                BeanUtils.copyProperties(investigationSignParam, investigationSignCommonUserParam);
                investigationSignCommonUserParam.setId(investigationSignParam.getReceiveUserId());
                investigationSignCommonUserService.addOrUpdate(investigationSignCommonUserParam);
            } catch (Exception e) {
                e.printStackTrace();
                log.info("添加常用人员失败,用户信息:{}", JSON.toJSONString(investigationSignParam));
            }
        }

    }

    @Override
    public void signed(InvestigationSign investigationSign) {
        //获取当前未签名的fileInfo
        SysFileInfo sysFileInfo = sysFileInfoService.getById(investigationSign.getPaperId());
        //获取签名图片byte
        byte[] imageBytes = null;
        String base64String = investigationSign.getSignFileBase64();

        // 检查并清理Base64字符串
        if (base64String != null) {
            // 移除可能存在的Base64前缀
            if (base64String.contains(",")) {
                base64String = base64String.substring(base64String.indexOf(",") + 1);
            }
            // 移除可能存在的换行符、空格等
            base64String = base64String.replaceAll("\\s", "");

            try {
                imageBytes = Base64.getDecoder().decode(base64String);

                // 旋转图片90度
                imageBytes = rotateImage(imageBytes, 90);

            } catch (IllegalArgumentException e) {
                log.error("Base64解码失败，签名ID：{}，错误：{}", investigationSign.getId(), e.getMessage());
                // 可以选择跳过此签名或使用默认签名
            }
        }
        //获取参数
        PdfSignatureParams pdfSignatureParams = getPdfSignatureParams(investigationSign.getPaperType());
        List<InvestigationSign> investigationSignList = this.list(new QueryWrapper<InvestigationSign>().lambda().eq(InvestigationSign::getPaperId, investigationSign.getPaperId()));
        //获取签名文件
        SysFileInfo fileInfo = signByPdfAndPosition(sysFileInfo, pdfSignatureParams, investigationSign.getSort(), imageBytes);
        //将同类签名文件替换为最新的
        investigationSignList.forEach(item -> {
            item.setPaperId(String.valueOf(fileInfo.getId()));
            this.updateById(item);
        });
        //如果未签名的file info里标记了bizid和biztype，移除未签名的，标记这个最新签名的
        if (ObjectUtil.isNotEmpty(sysFileInfo.getBizId()) && ObjectUtil.isNotEmpty(sysFileInfo.getBizType())) {
            fileInfo.setBizId(sysFileInfo.getBizId());
            fileInfo.setBizType(sysFileInfo.getBizType());
            if (ObjectUtil.isNotEmpty(sysFileInfo.getSignType())) {
                fileInfo.setSignType(sysFileInfo.getSignType());
            }
            sysFileInfoService.updateById(fileInfo);
            if ("BLLX".equals(sysFileInfo.getBizType())) {
                //笔录类型的文书生成新的前签名文书后续删除之前的
                sysFileInfoService.lambdaUpdate()
                        .set(SysFileInfo::getDelFlag, 1)
                        .eq(SysFileInfo::getId, sysFileInfo.getId())
                        .update();
            }
            //删除未签名的file info
//            sysFileInfoService.removeById(sysFileInfo.getId());
        }
        //流程里面存的文件对象要更新


    }

    @Override
    public SysFileInfo signByPdfAndPosition(SysFileInfo sysFileInfo, PdfSignatureParams params, int sort, byte[] bytes) {
        try {

            if (sysFileInfo == null) {
                return null;
            }

            // 读取PDF文件
            byte[] pdfBytes = getFileBytes(sysFileInfo);


            // 调用工具类在关键字后添加图片
            byte[] resultPdfBytes = PdfSignUtil.addSingleImageAtOrderedPositionAfterKeyword(
                    pdfBytes, params.getSearchText(),
                    params.getKeyWordIndex() != null ? params.getKeyWordIndex() : 0,
                    params.getOffsetX() != null ? params.getOffsetX() : 0f,
                    params.getOffsetY() != null ? params.getOffsetY() : 0f,
                    bytes,
                    sort,
                    params.getImageWidth() != null ? params.getImageWidth().floatValue() : 100f,
                    params.getImageHeight() != null ? params.getImageHeight().floatValue() : 50f,
                    params.getHorizontalGap() != null ? params.getHorizontalGap().floatValue() : 0f,
                    params.getVerticalGap() != null ? params.getVerticalGap().floatValue() : 0f,
                    params.getMarginLeft() != null ? params.getMarginLeft().floatValue() : 0f,
                    params.getMarginRight() != null ? params.getMarginRight().floatValue() : 0f,
                    params.getMarginBottom() != null ? params.getMarginBottom() : 0f);

            //存放到sysfileinfo
            try {
                // 创建MultipartFile对象
                String fileName = sysFileInfo.getFileOriginName();
                MultipartFile multipartFile = new MockMultipartFile(fileName, fileName, "application/pdf", resultPdfBytes);

                // 通过SysFileInfoService上传文件
                return sysFileInfoService.uploadFileOss(multipartFile, null, sysFileInfo.getSignType(), sysFileInfo);
            } catch (Exception ex) {
                log.error("上传签名PDF文件失败", ex);
                throw new ServiceException(SysFileInfoExceptionEnum.ERROR_FILE);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public void deleteOld(String id, String bizType) {
        this.remove(new QueryWrapper<InvestigationSign>().lambda().eq(InvestigationSign::getPid, id).eq(InvestigationSign::getPaperType, bizType));
    }

    @Override
    public SysFileInfo signByPosition(String id, float x, float y, Integer pageNo, String signId, String bizType, String infoId, String type) {
        //参数校验
        SysFileInfo sysFileInfo = sysFileInfoService.getById(id);
        if (sysFileInfo == null) {
            throw new ServiceException(500, "文件不存在");
        }
//        SignatureMaintenance signatureMaintenance = signatureMaintenanceService.getById(signId);
//        if (signatureMaintenance == null) {
//            throw new ServiceException(500, "签名不存在");
//        }
//        if (signatureMaintenance.getSealType() != 3) {
//            throw new ServiceException(500, "签名类型不正确");
//        }
        //oss地址转base64
        String base64 = null;
        try {
            base64 = getBase64ByOssUrl(sysFileInfo);
        } catch (IOException e) {
            throw new ServiceException(500, "文件不存在");
        }
        String signedBase64 = electronicSignatureUtil.signedBase64(base64, signId, sysFileInfo.getFileOriginName(), "1", String.valueOf((int) x), String.valueOf((int) y), String.valueOf(pageNo));
        if (ObjectUtil.isNotEmpty(signedBase64)) {
            //存放到sysfileinfo
            try {
                // 创建MultipartFile对象
                String fileName = sysFileInfo.getFileOriginName();
                MultipartFile multipartFile = new MockMultipartFile(fileName, fileName, "application/pdf", Base64.getDecoder().decode(signedBase64));
                // 通过SysFileInfoService上传文件
                SysFileInfo sFileInfo = sysFileInfoService.uploadFileOss(multipartFile, type, sysFileInfo.getSignType());
                if (ObjectUtil.isAllNotEmpty(infoId, bizType)) {
                    //删除旧的
                    sysFileInfoService.remove(new QueryWrapper<SysFileInfo>().lambda().eq(SysFileInfo::getBizId, id).eq(SysFileInfo::getBizType, bizType));
                    //配置新的
                    sysFileInfoService.setBiz(String.valueOf(sFileInfo.getId()), infoId, bizType);
                }
                //如果签名表里有旧id，替换为已盖章的最新id
                this.changeNewSignFileInfo(id, String.valueOf(sFileInfo.getId()));


                return sFileInfo;
            } catch (Exception ex) {
                log.error("上传签名PDF文件失败", ex);
                throw new ServiceException(SysFileInfoExceptionEnum.ERROR_FILE);
            }
        }
        return null;
    }

    @Override
    public SysFileInfo signByDevice(String id, String bizType, String infoId, String imgBase64Signature, String imgBase64Fingerprint) {
        SysFileInfo sysFileInfo = sysFileInfoService.getById(id);
        if (sysFileInfo == null) {
            throw new ServiceException(500, "文件不存在");
        }
        PdfSignatureParams params = getPdfSignatureParams(bizType);

        List<byte[]> imageBytesList = new java.util.ArrayList<>();

        try {
            imageBytesList.add(Base64.getDecoder().decode(imgBase64Signature.getBytes(StandardCharsets.UTF_8)));
            if (ObjectUtil.isNotEmpty(imgBase64Fingerprint)) {
                imageBytesList.add(Base64.getDecoder().decode(imgBase64Fingerprint.getBytes(StandardCharsets.UTF_8)));
            }
        } catch (Exception e) {
            throw new ServiceException(500, "无效的图片");
        }

        int signedCount = 0;
        String lastSignedLog = this.baseMapper.getDeviceSignedBefore(id);
        if (ObjectUtil.isNotEmpty(lastSignedLog)) {
            signedCount++;
            String preSingedLog = this.baseMapper.getDeviceSignedBefore(lastSignedLog);
            while (ObjectUtil.isNotEmpty(preSingedLog)) {
                signedCount++;
                preSingedLog = this.baseMapper.getDeviceSignedBefore(preSingedLog);
            }
        }

        try (InputStream inputStream = sysFileInfoService.download(sysFileInfo)) {
            // 如果不知道内容长度，使用可变大小缓冲区
            java.io.ByteArrayOutputStream outputStream = new java.io.ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            float imageHeight = params.getImageHeight() != null ? params.getImageHeight().floatValue() : 50f;
            byte[] resultPdfBytes = PdfSignUtil.addImagesOverlayAfterKeyword(
                    outputStream.toByteArray(), params.getSearchText(),
                    params.getKeyWordIndex() != null ? params.getKeyWordIndex() : 0,
                    params.getOffsetX() != null ? params.getOffsetX() : 0f, 10f,
                    params.getOffsetY() != null ? params.getOffsetY() - (imageHeight + 2) * signedCount : 0f - (imageHeight + 2) * signedCount, 0f,
                    imageBytesList, params.getImageWidth() != null ? params.getImageWidth().floatValue() : 100f,
                    imageHeight);
            //假设有n页，除最后一页外，其他页都要在右下角添加图片
            // 在每一页（除最后一页）的右下角添加所有签名图片
            if (!imageBytesList.isEmpty()) {
                // 添加所有图片到每页右下角，除了最后一页
                resultPdfBytes = PdfSignUtil.addMultipleImagesToAllPagesExceptLast(
                        resultPdfBytes,
                        imageBytesList,
                        params.getImageWidth() != null ? params.getImageWidth().floatValue() : 100f,
                        imageHeight,
                        20f,  // 20像素的页面边距
                        10f); // 10像素的图片间距
            }


            String fileName = sysFileInfo.getFileOriginName();
            SysFileInfo sFileInfo = sysFileInfoService.uploadFileOss(fileName, resultPdfBytes, sysFileInfo.getType(), sysFileInfo.getSignType());

            if (ObjectUtil.isAllNotEmpty(infoId, bizType)) {
                if (!"BLLX".equals(bizType)) {
                    //删除旧的
                    sysFileInfoService.lambdaUpdate().set(SysFileInfo::getDelFlag, 1).eq(SysFileInfo::getBizId, infoId).eq(SysFileInfo::getBizType, bizType).update();
                    //配置新的
                    sysFileInfoService.setBiz(String.valueOf(sFileInfo.getId()), infoId, bizType);
                } else {
                    //笔录类型的文书特殊处理
                    //删除旧的
                    sysFileInfoService.lambdaUpdate().set(SysFileInfo::getDelFlag, 1).eq(SysFileInfo::getBizId, infoId).eq(SysFileInfo::getBlId, sysFileInfo.getBlId()).eq(SysFileInfo::getBizType, bizType).update();
                    //配置新的
                    sysFileInfoService.lambdaUpdate()
                            .set(SysFileInfo::getDelFlag, 0)
                            .set(SysFileInfo::getBizType, bizType)
                            .set(SysFileInfo::getBizId, infoId)
                            .set(SysFileInfo::getBlId, sysFileInfo.getBlId())
                            .set(SysFileInfo::getFileBucket, sysFileInfo.getFileBucket())
                            .set(SysFileInfo::getCreateTime, sysFileInfo.getCreateTime()) //用于笔录文书页面展示的时候排序
                            .eq(SysFileInfo::getId, sFileInfo.getId())
                            .update();
                }
            }
            //如果签名表里有旧id，替换为已盖章的最新id
            this.changeNewSignFileInfo(id, String.valueOf(sFileInfo.getId()));

            return sFileInfo;
        } catch (IOException e) {
            throw new ServiceException(500, "文件不存在");
        }
    }

    private String getBase64ByOssUrl(SysFileInfo sysFileInfo) throws IOException {
        InputStream is = sysFileInfoService.download(sysFileInfo);
        byte[] data = IOUtils.toByteArray(is);
        return Base64.getEncoder().encodeToString(data);
    }

    @Override
    public void changeNewSignFileInfo(String oldId, String newId) {
        try {
            List<InvestigationSign> investigationSigns = this.list(new QueryWrapper<InvestigationSign>().lambda().eq(InvestigationSign::getPaperId, oldId));
            if (CollectionUtil.isNotEmpty(investigationSigns)) {
                for (InvestigationSign investigationSign : investigationSigns) {
                    investigationSign.setPaperId(newId);
                    this.updateById(investigationSign);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 旋转图片
     *
     * @param imageBytes 原始图片字节数组
     * @param degrees    旋转角度（正数为顺时针，负数为逆时针）
     * @return 旋转后的图片字节数组
     */
    private byte[] rotateImage(byte[] imageBytes, int degrees) {
        try {

            ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes);
            BufferedImage srcImage = ImageIO.read(bis);
            bis.close();

            if (srcImage == null) {
                log.error("无法读取图片数据");
                return imageBytes; // 返回原始图片
            }

            // 计算旋转后的图片尺寸
            int width = srcImage.getWidth();
            int height = srcImage.getHeight();

            // 向右旋转90度（顺时针）
            BufferedImage rotatedImage = new BufferedImage(height, width, srcImage.getType());
            for (int x = 0; x < width; x++) {
                for (int y = 0; y < height; y++) {
                    // 顺时针旋转90度: (x,y) -> (y, width-1-x)
                    rotatedImage.setRGB(y, width - 1 - x, srcImage.getRGB(x, y));
                }
            }

            // 将旋转后的图片转换为字节数组
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ImageIO.write(rotatedImage, "PNG", bos);
            byte[] rotatedImageBytes = bos.toByteArray();
            bos.close();

            return rotatedImageBytes;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    private PdfSignatureParams getPdfSignatureParams(String paperType) {
        switch (paperType) {
            //调查评估笔录
            case "BLLX":
                PdfSignatureParams paramsBl = new PdfSignatureParams();
                paramsBl.setSearchText("被调查人签名");
                paramsBl.setKeyWordIndex(0);
                paramsBl.setOffsetX(70.0f);
                paramsBl.setOffsetY(-10.0f);
                paramsBl.setImageWidth(60);
                paramsBl.setImageHeight(30);
                paramsBl.setHorizontalGap(0);
                paramsBl.setVerticalGap(30);
                paramsBl.setMarginLeft(150);
                paramsBl.setMarginRight(50);
                paramsBl.setMarginBottom(0.0f);
                return paramsBl;
            //小组合议
            case "opinion_deliberate":
                PdfSignatureParams params = new PdfSignatureParams();
                params.setSearchText("负责人签名");
                params.setKeyWordIndex(0);
                params.setOffsetX(70.0f);
                params.setOffsetY(0.0f);
                params.setImageWidth(60);
                params.setImageHeight(30);
                params.setHorizontalGap(0);
                params.setVerticalGap(30);
                params.setMarginLeft(150);
                params.setMarginRight(50);
                params.setMarginBottom(0.0f);
                return params;
            //小组合议调查评估表
            case "eval_deliberate":
                PdfSignatureParams reviewParams = new PdfSignatureParams();
                reviewParams.setSearchText("签");
                reviewParams.setKeyWordIndex(3);
                reviewParams.setOffsetX(30.0f);
                reviewParams.setOffsetY(0.0f);
                reviewParams.setImageWidth(60);
                reviewParams.setImageHeight(30);
                reviewParams.setHorizontalGap(0);
                reviewParams.setVerticalGap(20);
                reviewParams.setMarginLeft(130);
                reviewParams.setMarginRight(50);
                reviewParams.setMarginBottom(0.0f);
                return reviewParams;
            //集体评议
            case "opinion_review":
                PdfSignatureParams opinionParams = new PdfSignatureParams();
                opinionParams.setSearchText("负责人签名");
                opinionParams.setKeyWordIndex(0);
                opinionParams.setOffsetX(70.0f);
                opinionParams.setOffsetY(0.0f);
                opinionParams.setImageWidth(60);
                opinionParams.setImageHeight(30);
                opinionParams.setHorizontalGap(0);
                opinionParams.setVerticalGap(30);
                opinionParams.setMarginLeft(150);
                opinionParams.setMarginRight(80);
                opinionParams.setMarginBottom(0.0f);
                return opinionParams;
            //集体评议调查评估表
            case "eval_review":
                PdfSignatureParams evalReviewParams = new PdfSignatureParams();
                evalReviewParams.setSearchText("签");
                evalReviewParams.setKeyWordIndex(4);
                evalReviewParams.setOffsetX(30.0f);
                evalReviewParams.setOffsetY(0.0f);
                evalReviewParams.setImageWidth(60);
                evalReviewParams.setImageHeight(30);
                evalReviewParams.setHorizontalGap(0);
                evalReviewParams.setVerticalGap(20);
                evalReviewParams.setMarginLeft(130);
                evalReviewParams.setMarginRight(50);
                evalReviewParams.setMarginBottom(0.0f);
                return evalReviewParams;
            //审批
            case "eval_approval":
                PdfSignatureParams approvalParams = new PdfSignatureParams();
                approvalParams.setSearchText("签");
                approvalParams.setKeyWordIndex(5);
                approvalParams.setOffsetX(30.0f);
                approvalParams.setOffsetY(0.0f);
                approvalParams.setImageWidth(60);
                approvalParams.setImageHeight(30);
                approvalParams.setHorizontalGap(0);
                approvalParams.setVerticalGap(20);
                approvalParams.setMarginLeft(130);
                approvalParams.setMarginRight(50);
                approvalParams.setMarginBottom(0.0f);
                return approvalParams;
            default:
                PdfSignatureParams defaultParams = new PdfSignatureParams();
                defaultParams.setSearchText("负责人签名");
                defaultParams.setKeyWordIndex(0);
                defaultParams.setOffsetX(70.0f);
                defaultParams.setOffsetY(0.0f);
                defaultParams.setImageWidth(60);
                defaultParams.setImageHeight(30);
                defaultParams.setHorizontalGap(0);
                defaultParams.setVerticalGap(30);
                defaultParams.setMarginLeft(150);
                defaultParams.setMarginRight(50);
                defaultParams.setMarginBottom(0.0f);
                return defaultParams;
        }
    }

    private String exchangePaperTypeName(String paperType) {
        switch (paperType) {
            case "opinion_deliberate":
                return "小组合议意见表";
            case "eval_deliberate":
                return "社区矫正调查评估表";
            case "opinion_review":
                return "集体评议意见表";
            case "eval_review":
                return "社区矫正调查评估表";
            case "eval_approval":
                return "社区矫正调查评估表";
            case "BLLX":
                return "调查评估笔录";
            default:
                return "小组合议";
        }

    }

    private byte[] getFileBytes(SysFileInfo sysFileInfo) throws IOException {
        // 读取文件内容
        try (InputStream inputStream = sysFileInfoService.download(sysFileInfo)) {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            return outputStream.toByteArray();
        }
    }

    private byte[] base64ToBytes(String base64String) {
        if (base64String != null) {
            // 移除可能存在的Base64前缀
            if (base64String.contains(",")) {
                base64String = base64String.substring(base64String.indexOf(",") + 1);
            }
            // 移除可能存在的换行符、空格等
            base64String = base64String.replaceAll("\\s", "");

            try {
                return Base64.getDecoder().decode(base64String);
            } catch (IllegalArgumentException e) {
                log.error("Base64解码失败，错误：{}", e.getMessage());
            }
        }
        return null;
    }

    private byte[] base64ImageToBytesAndRotate90(String base64String) {
        return rotateImage(base64ToBytes(base64String), 90);
    }


    //不旋转90度
    private byte[] base64ToBytesNotSpin(String base64String) {
        if (base64String != null) {
            // 移除可能存在的Base64前缀
            if (base64String.contains(",")) {
                base64String = base64String.substring(base64String.indexOf(",") + 1);
            }
            // 移除可能存在的换行符、空格等
            base64String = base64String.replaceAll("\\s", "");

            try {
                byte[] imageBytes = Base64.getDecoder().decode(base64String);
                return imageBytes;
            } catch (IllegalArgumentException e) {
                log.error("Base64解码失败，错误：{}", e.getMessage());
                // 可以选择跳过此签名或使用默认签名
            }
        }
        return null;
    }

    @Override
    public void batchSign(String paperId, HttpServletResponse response) {
        try {
            List<InvestigationSign> investigationSignList = this.list(new LambdaQueryWrapper<InvestigationSign>().eq(InvestigationSign::getPaperId, paperId).eq(InvestigationSign::getStatus, "1"));
            if (CollectionUtil.isEmpty(investigationSignList)) {
                response.sendError(500, "未找到待签记录");
                return;
            }
            // 获取PDF文件
            SysFileInfo pdfFileInfo = sysFileInfoService.getById(paperId);
            if (ObjectUtil.isEmpty(pdfFileInfo)) {
                response.sendError(500, "未找到PDF文件");
                return;
            }
            // 获取PDF文件字节数组
            byte[] pdfBytes = getFileBytes(pdfFileInfo);
            // 签名图片base64转byte
            List<byte[]> imageBytesList = new ArrayList<>();
            for (InvestigationSign investigationSign : investigationSignList) {
                String base64String = investigationSign.getSignFileBase64();

                byte[] bytes = base64ImageToBytesAndRotate90(base64String);
                if (bytes != null) {
                    imageBytesList.add(bytes);
                }
            }
            //根据文书类型来调整参数并签名
            PdfSignatureParams params = getPdfSignatureParams(investigationSignList.get(0).getPaperType());
            // 调用工具类在关键字后添加图片
            byte[] resultPdfBytes = PdfSignUtil.addImagesAfterKeyword(
                    pdfBytes, params.getSearchText(),
                    params.getKeyWordIndex() != null ? params.getKeyWordIndex() : 0,
                    params.getOffsetX() != null ? params.getOffsetX() : 0f,
                    params.getOffsetY() != null ? params.getOffsetY() : 0f,
                    imageBytesList,
                    params.getImageWidth() != null ? params.getImageWidth().floatValue() : 100f,
                    params.getImageHeight() != null ? params.getImageHeight().floatValue() : 50f,
                    params.getHorizontalGap() != null ? params.getHorizontalGap().floatValue() : 0f,
                    params.getVerticalGap() != null ? params.getVerticalGap().floatValue() : 0f,
                    params.getMarginLeft() != null ? params.getMarginLeft().floatValue() : 0f,
                    params.getMarginRight() != null ? params.getMarginRight().floatValue() : 0f,
                    params.getMarginBottom() != null ? params.getMarginBottom() : 0f);

            // 设置响应头
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "inline; filename=signed_" + pdfFileInfo.getFileOriginName());

            // 输出处理后的PDF
            response.getOutputStream().write(resultPdfBytes);
            response.flushBuffer();
        } catch (Exception e) {
            try {
                log.error("批量签名失败", e);
                response.sendError(500, "批量签名失败: " + e.getMessage());
            } catch (IOException ex) {
                // 忽略
            }
        }
    }

    @Override
    public String batchSignBase64(String base64Pdf, PdfSignatureParams params, List<byte[]> bytesList) {
        // 调用工具类在关键字后添加图片
        byte[] pdfBytes = base64ToBytes(base64Pdf);
        return batchSignBase64(pdfBytes, params, bytesList);
    }

    private String batchSignBase64(byte[] pdfBytes, PdfSignatureParams params, List<byte[]> bytesList) {
        try {
            byte[] resultPdfBytes = PdfSignUtil.addImagesAfterKeyword(
                    pdfBytes, params.getSearchText(),
                    params.getKeyWordIndex() != null ? params.getKeyWordIndex() : 0,
                    params.getOffsetX() != null ? params.getOffsetX() : 0f,
                    params.getOffsetY() != null ? params.getOffsetY() : 0f,
                    bytesList,
                    params.getImageWidth() != null ? params.getImageWidth().floatValue() : 100f,
                    params.getImageHeight() != null ? params.getImageHeight().floatValue() : 50f,
                    params.getHorizontalGap() != null ? params.getHorizontalGap().floatValue() : 0f,
                    params.getVerticalGap() != null ? params.getVerticalGap().floatValue() : 0f,
                    params.getMarginLeft() != null ? params.getMarginLeft().floatValue() : 0f,
                    params.getMarginRight() != null ? params.getMarginRight().floatValue() : 0f,
                    params.getMarginBottom() != null ? params.getMarginBottom() : 0f);

            return bytesToBase64(resultPdfBytes);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private String bytesToBase64(byte[] resultPdfBytes) {
        return Base64.getEncoder().encodeToString(resultPdfBytes);
    }

    @Override
    public void addImages(PdfSignatureParams params, HttpServletResponse response) {
        try {
            // 获取原始PDF文件
            SysFileInfo pdfFileInfo = sysFileInfoService.getById(params.getFileId());
            if (pdfFileInfo == null) {
                response.sendError(500, "PDF文件不存在");
                return;
            }

            // 读取PDF文件
            byte[] pdfBytes = getFileBytes(pdfFileInfo);

            // 解析图片ID列表并获取图片字节
            String[] imageIdArray = params.getImageFileIds().split(",");
            if (imageIdArray.length == 0) {
                response.sendError(500, "未提供图片ID");
                return;
            }

            List<byte[]> imageBytesList = new java.util.ArrayList<>();
            for (String imageIdStr : imageIdArray) {
                try {
                    Long imageId = Long.parseLong(imageIdStr.trim());
                    SysFileInfo imageFileInfo = sysFileInfoService.getById(imageId);
                    if (imageFileInfo != null) {
                        imageBytesList.add(getFileBytes(imageFileInfo));
                    } else {
                        // 处理图片未找到的情况，可以选择跳过或报错
                        response.sendError(500, "图片文件不存在: ID=" + imageId);
                        return;
                    }
                } catch (NumberFormatException e) {
                    response.sendError(500, "无效的图片ID格式: " + imageIdStr);
                    return;
                }
            }

            if (imageBytesList.isEmpty()) {
                response.sendError(500, "未能成功加载任何图片");
                return;
            }

            // 调用工具类在关键字后添加图片
            byte[] resultPdfBytes = PdfSignUtil.addImagesAfterKeyword(
                    pdfBytes, params.getSearchText(),
                    params.getKeyWordIndex() != null ? params.getKeyWordIndex() : 0,
                    params.getOffsetX() != null ? params.getOffsetX() : 0f,
                    params.getOffsetY() != null ? params.getOffsetY() : 0f,
                    imageBytesList,
                    params.getImageWidth() != null ? params.getImageWidth().floatValue() : 100f,
                    params.getImageHeight() != null ? params.getImageHeight().floatValue() : 50f,
                    params.getHorizontalGap() != null ? params.getHorizontalGap().floatValue() : 0f,
                    params.getVerticalGap() != null ? params.getVerticalGap().floatValue() : 0f,
                    params.getMarginLeft() != null ? params.getMarginLeft().floatValue() : 0f,
                    params.getMarginRight() != null ? params.getMarginRight().floatValue() : 0f,
                    params.getMarginBottom() != null ? params.getMarginBottom() : 0f);

            // 设置响应头
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition",
                    "inline; filename=signed_" + pdfFileInfo.getFileOriginName());

            // 输出处理后的PDF
            response.getOutputStream().write(resultPdfBytes);
            response.flushBuffer();

        } catch (Exception e) {
            try {
                response.sendError(500, "处理PDF签名失败: " + e.getMessage());
            } catch (IOException ex) {
                // 忽略
            }
        }
    }

    @Override
    public ResponseData findTextLocations(PdfSignatureParams params) {
        try {
            // 获取PDF文件
            SysFileInfo pdfFileInfo = sysFileInfoService.getById(params.getFileId());
            if (pdfFileInfo == null) {
                return ResponseData.error("PDF文件不存在");
            }

            // 读取PDF文件
            byte[] pdfBytes = getFileBytes(pdfFileInfo);

            // 定位文本
            List<PdfSignUtil.TextLocation> locations = PdfSignUtil.locateTextInPdf(pdfBytes, params.getSearchText());

            if (locations.isEmpty()) {
                return ResponseData.success("未找到匹配的文本: " + params.getSearchText());
            }

            // 返回位置信息
            return ResponseData.success(locations);
        } catch (Exception e) {
            return ResponseData.error("查找文本位置失败: " + e.getMessage());
        }
    }

    @Override
    public byte[] test(String fileId, String signId, Integer sort) {
        SysFileInfo sysFileInfo = sysFileInfoService.getById(fileId);
        InvestigationSign investigationSign = this.getById(signId);
        //获取签名图片byte
        byte[] imageBytes = null;
        String base64String = investigationSign.getSignFileBase64();

        // 检查并清理Base64字符串
        if (base64String != null) {
            // 移除可能存在的Base64前缀
            if (base64String.contains(",")) {
                base64String = base64String.substring(base64String.indexOf(",") + 1);
            }
            // 移除可能存在的换行符、空格等
            base64String = base64String.replaceAll("\\s", "");

            try {
                imageBytes = Base64.getDecoder().decode(base64String);

                // 旋转图片90度
                imageBytes = rotateImage(imageBytes, 90);

            } catch (IllegalArgumentException e) {
                log.error("Base64解码失败，签名ID：{}，错误：{}", investigationSign.getId(), e.getMessage());
                // 可以选择跳过此签名或使用默认签名
            }
        }
        PdfSignatureParams params = getPdfSignatureParams(investigationSign.getPaperType());
        try {
            // 读取PDF文件
            byte[] pdfBytes = getFileBytes(sysFileInfo);

            // 调用工具类在关键字后添加图片
            return PdfSignUtil.addSingleImageAtOrderedPositionAfterKeyword(
                    pdfBytes, params.getSearchText(),
                    params.getKeyWordIndex() != null ? params.getKeyWordIndex() : 0,
                    params.getOffsetX() != null ? params.getOffsetX() : 0f,
                    params.getOffsetY() != null ? params.getOffsetY() : 0f,
                    imageBytes,
                    sort,
                    params.getImageWidth() != null ? params.getImageWidth().floatValue() : 100f,
                    params.getImageHeight() != null ? params.getImageHeight().floatValue() : 50f,
                    params.getHorizontalGap() != null ? params.getHorizontalGap().floatValue() : 0f,
                    params.getVerticalGap() != null ? params.getVerticalGap().floatValue() : 0f,
                    params.getMarginLeft() != null ? params.getMarginLeft().floatValue() : 0f,
                    params.getMarginRight() != null ? params.getMarginRight().floatValue() : 0f,
                    params.getMarginBottom() != null ? params.getMarginBottom() : 0f);
        } catch (Exception e) {
            log.error("PDF签名处理失败", e);
            throw new ServiceException(500, "PDF签名处理失败：" + e.getMessage());
        }
    }

    @Override
    public ResponseData check(String id, String bizType) {
        List<InvestigationSign> notSignList = getNoSignList(id, bizType);
        if (CollectionUtil.isNotEmpty(notSignList)) {
            Set<String> collect = notSignList.stream().map(InvestigationSign::getTitle).collect(Collectors.toSet());
            String join = String.join(",", collect);
            return ResponseData.error("当前页面" + join + "发送的签名请求尚未全部签好，提交后未签名的将会自动过期，只保留已签名部分。请确认是否继续提交");
        }
        return ResponseData.success();
    }

    @Override
    public void handleNoSign(String id, String bizType) {
        try {
            List<InvestigationSign> notSignList = getNoSignList(id, bizType);
            for (InvestigationSign investigationSign : notSignList) {
                investigationSign.setStatus("3");
                this.updateById(investigationSign);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private List<InvestigationSign> getNoSignList(String id, String bizType) {
        QueryWrapper<InvestigationSign> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(InvestigationSign::getPid, id);
        if ("PGZT03".equals(bizType)) {
            queryWrapper.lambda().and(q -> q.eq(InvestigationSign::getPaperType, "BLLX").or().eq(InvestigationSign::getPaperType, "BLLX"));
        }
        if ("PGZT04".equals(bizType)) {
            queryWrapper.lambda().and(q -> q.eq(InvestigationSign::getPaperType, "opinion_deliberate").or().eq(InvestigationSign::getPaperType, "eval_deliberate"));
        }
        if ("PGZT05".equals(bizType)) {
            queryWrapper.lambda().and(q -> q.eq(InvestigationSign::getPaperType, "opinion_review").or().eq(InvestigationSign::getPaperType, "eval_review"));
        }
        if ("PGZT06".equals(bizType)) {
            queryWrapper.lambda().eq(InvestigationSign::getPaperType, "eval_approval");
        }
        queryWrapper.lambda().eq(InvestigationSign::getStatus, 0);
        return this.list(queryWrapper);
    }

    @Override
    public String retroSignature(String id, String fileIds, String bizType) {
        InvestigationSign one = this.getOne(new QueryWrapper<InvestigationSign>().lambda().eq(InvestigationSign::getPid, id).eq(InvestigationSign::getPaperType, bizType).last("limit 1"));
        if (ObjectUtil.isNotEmpty(one)) {
            if (!fileIds.contains(one.getPaperId())) {
                return one.getPaperId();
            }
        }
        return fileIds;
    }

    @Override
    public byte[] getSinglePointAllDeviceSignedLog(String pdfBase64, String id, String bizType, float offsetY) {
        List<HashMap<String, String>> all = new ArrayList<>();
        byte[] pdfBytes = base64ToBytes(pdfBase64);
        HashMap<String, String> lastLog = this.baseMapper.getDeviceSignHistory(id, bizType);
        if (lastLog == null) {
            return pdfBytes;
        }
        all.add(lastLog);
        HashMap<String, String> preLog = this.baseMapper.getDeviceSignHistoryByFileAfterId(lastLog.get("fileBeforeId"));
        while (preLog != null) {
            all.add(preLog);
            preLog = this.baseMapper.getDeviceSignHistoryByFileAfterId(preLog.get("fileBeforeId"));
        }
        List<HashMap<String, String>> reverse = ListUtil.reverse(all);
        PdfSignatureParams params = getPdfSignatureParams(bizType);
        List<List<byte[]>> batchImageBytes = new ArrayList<>();
        try {
            for (HashMap<String, String> stringHashMap : reverse) {
                List<byte[]> imageBytesList = new ArrayList<>();
                imageBytesList.add(Base64.getDecoder().decode(stringHashMap.get("imgBase64Signature").getBytes(StandardCharsets.UTF_8)));
                if (ObjectUtil.isNotEmpty(stringHashMap.get("imgBase64Fingerprint"))) {
                    imageBytesList.add(Base64.getDecoder().decode(stringHashMap.get("imgBase64Fingerprint").getBytes(StandardCharsets.UTF_8)));
                }
                batchImageBytes.add(imageBytesList);
            }

            if (batchImageBytes.size() > 0) {
                offsetY = offsetY - params.getImageHeight() * reverse.size();
                return PdfSignUtil.addImagesOverlayAfterKeywordBatch(
                        pdfBytes, params.getSearchText(),
                        params.getKeyWordIndex() != null ? params.getKeyWordIndex() : 0,
                        params.getOffsetX() != null ? params.getOffsetX() : 0f, 10f,
                        params.getOffsetY() != null ? params.getOffsetY() : 0f, params.getImageHeight() != null ? 0f - params.getImageHeight().floatValue() : -50f,
                        batchImageBytes, params.getImageWidth() != null ? params.getImageWidth().floatValue() : 100f,
                        params.getImageHeight() != null ? params.getImageHeight().floatValue() : 50f);
            }
            return pdfBytes;

        } catch (Exception e) {
            throw new ServiceException(500, "无效的图片");
        }
    }

    @Override
    public String mergeSignature(String base64, String pgzt, String id, String bizType) {
        //集体合议，看看前面
        if ("PGZT05".equals(pgzt)) {
            PdfSignatureParams pdfSignatureParams = getPdfSignatureParams("eval_deliberate");
            float offsetY = pdfSignatureParams.getOffsetY();
            byte[] pdfBytes = getSinglePointAllDeviceSignedLog(base64, id, "eval_deliberate", offsetY);

            //先拿签名列表，如果有才继续下一步
            List<InvestigationSign> list = this.list(new QueryWrapper<InvestigationSign>().lambda().eq(InvestigationSign::getPid, id).eq(InvestigationSign::getStatus, "1").eq(InvestigationSign::getPaperType, "eval_deliberate").orderByAsc(InvestigationSign::getSort));
            if (CollectionUtil.isNotEmpty(list)) {
                List<byte[]> bytesList = new ArrayList<>();
                for (InvestigationSign investigationSign : list) {
                    byte[] bytes = base64ImageToBytesAndRotate90(investigationSign.getSignFileBase64());
                    if (bytes != null) {
                        bytesList.add(bytes);
                    }
                }
                // 有设备签名时重新设定向下偏移
                pdfSignatureParams.setOffsetY(offsetY);
                return batchSignBase64(pdfBytes, pdfSignatureParams, bytesList);
            } else {
                return bytesToBase64(pdfBytes);
            }

        }
        //审批
        if ("PGZT06".equals(pgzt)) {
            //先签合议
            base64 = mergeSignature(base64, "PGZT05", id, bizType);

            PdfSignatureParams pdfSignatureParams = getPdfSignatureParams("eval_review");
            float offsetY = pdfSignatureParams.getOffsetY();
            byte[] pdfBytes = getSinglePointAllDeviceSignedLog(base64, id, "eval_review", offsetY);

            //先拿签名列表，如果有才继续下一步
            List<InvestigationSign> list = this.list(new QueryWrapper<InvestigationSign>().lambda().eq(InvestigationSign::getPid, id).eq(InvestigationSign::getStatus, "1").eq(InvestigationSign::getPaperType, "eval_review").orderByAsc(InvestigationSign::getSort));
            if (CollectionUtil.isNotEmpty(list)) {
                List<byte[]> bytesList = new ArrayList<>();
                for (InvestigationSign investigationSign : list) {
                    byte[] bytes = base64ImageToBytesAndRotate90(investigationSign.getSignFileBase64());
                    if (bytes != null) {
                        bytesList.add(bytes);
                    }
                }
                // 有设备签名时重新设定向下偏移
                pdfSignatureParams.setOffsetY(offsetY);
                return batchSignBase64(pdfBytes, pdfSignatureParams, bytesList);
            } else {
                return bytesToBase64(pdfBytes);
            }

        }
        return null;
    }


    private String fileToBase64(File file) {
        try {
            // 创建文件输入流
            FileInputStream fileInputStream = new FileInputStream(file);
            // 创建字节数组
            byte[] bytes = new byte[(int) file.length()];
            // 读取文件内容到字节数组
            fileInputStream.read(bytes);
            // 关闭输入流
            fileInputStream.close();
            // 将字节数组转换为Base64字符串
            return Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            log.error("文件转Base64失败", e);
            throw new ServiceException(500, "文件转Base64失败");
        }
    }

    private File base64ToFile(String base64) {
        try {
            // 创建临时文件
            File tempFile = File.createTempFile("temp_", ".pdf");
            // 将Base64字符串解码为字节数组
            byte[] bytes = Base64.getDecoder().decode(base64);
            // 创建文件输出流
            FileOutputStream fileOutputStream = new FileOutputStream(tempFile);
            // 将字节数组写入文件
            fileOutputStream.write(bytes);
            // 关闭输出流
            fileOutputStream.close();
            return tempFile;
        } catch (Exception e) {
            log.error("Base64转文件失败", e);
            throw new ServiceException(500, "Base64转文件失败");
        }
    }

    @Override
    public String preSign(String id, String base64Sign) {
        try {
            InvestigationSign investigationSign = this.getById(id);
            SysFileInfo sysFileInfo = sysFileInfoService.getById(investigationSign.getPaperId());
            PdfSignatureParams pdfSignatureParams = getPdfSignatureParams(investigationSign.getPaperType());
            int sort = 1;
            QueryWrapper<InvestigationSign> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(InvestigationSign::getPaperId, investigationSign.getPaperId());
            queryWrapper.lambda().orderByDesc(InvestigationSign::getSort);
            List<InvestigationSign> list = this.list(queryWrapper);
            if (CollectionUtil.isNotEmpty(list)) {
                InvestigationSign sign1 = list.get(0);
                if (ObjectUtil.isNotEmpty(sign1.getSort())) {
                    sort = sign1.getSort() + 1;
                }
            }
            SysFileInfo fileInfo = signByPdfAndPosition(sysFileInfo, pdfSignatureParams, sort, base64ImageToBytesAndRotate90(base64Sign));
            return OssSignedUrlUtil.generatePresignedUrlWithPublicDomain(fileInfo.getFilePath());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public SysFileInfo preSignBl(String id, String base64Sign) {
        try {
            SysFileInfo sysFileInfo = sysFileInfoService.getById(id);
            PdfSignatureParams pdfSignatureParams = getPdfSignatureParams("BLLX");
            int sort = 1;
            SysFileInfo fileInfo = signByPdfAndPosition(sysFileInfo, pdfSignatureParams, sort, base64ImageToBytesAndRotate90(base64Sign));
            return fileInfo;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public SysFileInfo preSignBlBland(String id, String base64Sign) {
        try {
            SysFileInfo sysFileInfo = sysFileInfoService.getById(id);
            PdfSignatureParams pdfSignatureParams = getPdfSignatureParams("BLLX");
            int sort = 1;
            SysFileInfo fileInfo = signByPdfAndPosition(sysFileInfo, pdfSignatureParams, sort, base64ToBytesNotSpin(base64Sign));
            return fileInfo;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public String subSign(InvestigationTranscriptParam investigationTranscriptParam) {
        try {
            investigationTranscriptParam.setTag(1);
            InvestigationTranscript tt = investigationTranscriptService.fillAndSaveTranscript(investigationTranscriptParam);
            //删除数据库目前的笔录id
            sysFileInfoService.lambdaUpdate()
                    .set(SysFileInfo::getDelFlag, 1)
                    .eq(SysFileInfo::getBizId, investigationTranscriptParam.getPid())
                    .eq(SysFileInfo::getBlId, investigationTranscriptParam.getId())
                    .eq(SysFileInfo::getBizType, "BLLX")
                    .update();
            //转存新的
            sysFileInfoService.lambdaUpdate()
                    .set(SysFileInfo::getDelFlag, 0)
                    .set(SysFileInfo::getBizType, "BLLX")
                    .set(SysFileInfo::getBizId, investigationTranscriptParam.getPid())
                    .set(SysFileInfo::getBlId, investigationTranscriptParam.getId())
                    .set(SysFileInfo::getFileBucket, investigationTranscriptParam.getPaperType())
                    .eq(SysFileInfo::getId, investigationTranscriptParam.getFileId())
                    .update();
            return "";
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }

    @Override
    public SysFileInfo signByDrag(String id, float x, float y, float width, float height, Integer pageNo, String signFileBase64) {
        SysFileInfo sysFileInfo = sysFileInfoService.getById(id);
        if (ObjectUtil.isNull(sysFileInfo)) {
            throw new ServiceException(500, "文件不存在");
        }
        try {
            byte[] resultPdfBytes = PdfSignUtil.addSingleImageAtOrderedPosition(getFileBytes(sysFileInfo),
                    base64ToBytesNotSpin(signFileBase64),
                    pageNo, 1, x, y, width == 0f ? 100f : width,
                    height == 0f ? 50f : height, 0f, 0f, 0f, 0f, 0f
            );
            // 创建MultipartFile对象
            String fileName = sysFileInfo.getFileOriginName();
            MultipartFile multipartFile = new MockMultipartFile(fileName, fileName, "application/pdf", resultPdfBytes);
            // 通过SysFileInfoService上传文件
            sysFileInfo.setBizType("BLLX");
            return sysFileInfoService.uploadFileOss(multipartFile, "1", sysFileInfo.getSignType(), sysFileInfo);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("指定坐标签名失败", e);
            return null;
        }
    }
}
