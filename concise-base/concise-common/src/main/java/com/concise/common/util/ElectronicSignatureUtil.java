package com.concise.common.util;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.security.Key;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.concise.common.exception.ServiceException;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.response.ResponseData;
import com.sun.org.apache.xerces.internal.impl.dv.util.Base64;
import com.timvale.sealplatform.bean.PosBean;
import com.timvale.sealplatform.result.SealDetailResult;
import com.timvale.sealplatform.result.file.SignPDFResult;
import com.timvale.sealplatform.sdk.ClientManageService;
import com.timvale.sealplatform.sdk.config.HttpConnectionConfig;
import com.timvale.sealplatform.sdk.config.ProjectConfig;
import com.timvale.sealplatform.sdk.file.SignLocalClient;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import sun.misc.BASE64Encoder;

/**
 * 电子签章工具类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ElectronicSignatureUtil {
    @Value("${electronicSignature.project-id}")
    private String projectId;

    @Value("${electronicSignature.project-secret}")
    private String projectSecret;

    @Value("${electronicSignature.address}")
    private String address;

    @Value("${electronicSignature.domain-name}")
    private String domainName;

    // 静态实例，用于向后兼容
    private static ElectronicSignatureUtil instance;
    @Resource
    private SysFileInfoService sysFileInfoService;

    @PostConstruct
    public void init() {
        instance = this;
    }

    // 静态便捷方法
    public static String signedBase64Static(String base64, String sealSn, String fileName, String signType, String posX, String posY) {
        return instance.signedBase64(base64, sealSn, fileName, signType, posX, posY);
    }

    public static String signedBase64Static(String base64, String sealSn, String fileName, String signType, String posX, String posY, String posPage) {
        return instance.signedBase64(base64, sealSn, fileName, signType, posX, posY, posPage);
    }

    public static String signedBase64Static(String base64, String sealSn, String fileName, String key) {
        return instance.signedBase64(base64, sealSn, fileName, key);
    }

    public static SignLocalClient getSignLocalClientStatic() {
        return instance.getSignLocalClient();
    }

    public static ResponseData getSignPictureSdksStatic(String sealSn) {
        return instance.getSignPictureSdks(sealSn);
    }

    /**
     * 获取盖章后的pdf的base64文件
     *
     * @param base64   原pdf的base64
     * @param sealSn   印章编号
     * @param fileName 原pdf文件名称
     * @return
     */
    public String signedBase64(String base64, String sealSn, String fileName, String signType, String posX, String posY) {
        PosBean posBean = new PosBean();
        posBean.setPosX(Integer.parseInt(posX));
        posBean.setPosY(Integer.parseInt(posY));
        posBean.setPosPage("1");
        return signedBase64Sdk(base64, sealSn, Integer.valueOf(signType), posBean);
    }

    public String signedBase64(String base64, String sealSn, String fileName, String signType, String posX, String posY, String posPage) {
        PosBean posBean = new PosBean();
        posBean.setPosX(Integer.parseInt(posX));
        posBean.setPosY(Integer.parseInt(posY));
        posBean.setPosPage(posPage);
        return signedBase64Sdk(base64, sealSn, Integer.valueOf(signType), posBean);
    }

    public SignLocalClient getSignLocalClient() {
        ProjectConfig projectConfig = new ProjectConfig();
        projectConfig.setAppId(projectId);
        projectConfig.setSecret(projectSecret);
        projectConfig.setDomainName(domainName);

        HttpConnectionConfig httpConnectionConfig = new HttpConnectionConfig();
        httpConnectionConfig.setProtocol("http");
        httpConnectionConfig.setConnectionTimeOut(3000);
        return ClientManageService.initClient(projectConfig, httpConnectionConfig);
    }

    public String signedBase64Sdk(String base64, String sealSn, Integer signType, PosBean posBean) {
        byte[] bytes = java.util.Base64.getDecoder().decode(base64);
        SignPDFResult signPDFResult = getSignLocalClient().localSignPDF(bytes, sealSn, signType, posBean, "");
        if (signPDFResult.getErrCode() == 0) {
            return java.util.Base64.getEncoder().encodeToString(signPDFResult.getOutByte());
        } else {
            System.out.println(signPDFResult);
            throw new RuntimeException(signPDFResult.getMsg());
        }
    }


    /**
     * 关键字盖章
     *
     * @param base64   原pdf的base64
     * @param sealSn   印章编号
     * @param fileName 原pdf文件名称
     * @param key      关键字
     * @return pdf的base64
     */
    public String signedBase64(String base64, String sealSn, String fileName, String key) {
        PosBean posBean = new PosBean();
        posBean.setKey(key);
        posBean.setPosType("1");
        return signedBase64Sdk(base64, sealSn, 4, posBean);
    }

    public String getSignPicture(String sealSn, String sealUseType) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("sealSn", sealSn);
        jsonObject.put("sealUseType", sealUseType);
        try {
            return post("/seal-platform/seal/v1/rest/sign/getSealList", jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public ResponseData getSignPictureSdks(String sealSn) {
        Map<String, Object> map = new HashMap<>();
        //1、检测章是否存在
        try {
            SealDetailResult sealDetail = getSignLocalClient().getSealDetail(sealSn);
            map.put("sealDetail", sealDetail);
            map.put("sealMsg", "印章存在");
        } catch (RuntimeException e) {
            map.put("sealDetail", null);
            map.put("sealMsg", e.getMessage());
        }
        //检测章是否有效
        try {
            //生成空白pdf盖章，如果正常返回就是有效
            

            String signeded = signedBase64(, sealSn, "数据协同表单.pdf", "1", "0", "0");
            if (ObjectUtil.isEmpty(signeded)) {
                map.put("sealStatus", false);
                map.put("sealStatusMsg", "印章无效");
            }else {
                map.put("sealStatus", true);
                map.put("sealStatusMsg", "印章有效");
            }
            return ResponseData.success(map);
        } catch (RuntimeException e) {
            map.put("sealStatus", null);
            map.put("sealStatusMsg", e.getMessage());
        }
        return ResponseData.success(map);
    }

    /**
     * * pdf文件盖章
     * * 接口地址：/V1/accounts/outerAccounts/create
     *
     * @param base64   pdf文件base64
     * @param sealSn   印章编号
     * @param fileName pdf文件名
     * @param signType 签署类型 1 单页签 2多页签 3 骑缝签 4关键字签
     * @param posX     x坐标
     * @param posY     y坐标
     * @return
     */
    public JSONObject createSignPdf(String base64, String sealSn, String fileName, String signType, String posX, String posY) {
        JSONObject obj = null;
        String resp = null;
        try {
            JSONObject reqData = new JSONObject();
            reqData.put("fileBase64", base64);
            reqData.put("sealSn", sealSn);
            reqData.put("posX", posX);
            reqData.put("posY", posY);
            reqData.put("signType", signType);
            //页码，若为关键字定位此项无需传递，默认1
            reqData.put("posPage", "1");
            reqData.put("fileName", fileName);
            resp = post("/seal-platform/seal/v1/rest/sign/signPdf", reqData);
            obj = JSONObject.parseObject(resp);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return obj;
    }


    public String post(String apiUrl, JSONObject data) throws Exception {
        apiUrl = address + apiUrl;

        // 请求数据转为JSON字节流,作为HTTP请求体
        byte[] stream = data.toString().getBytes("UTF-8");
        // 签名数据,根据签名算法,对请求数据进行签名
        String signature = sign(stream);

        // 设置HTTP请求头
        HttpEntityEnclosingRequestBase req = new HttpPost(apiUrl);
        // project-id为用户的projectId
        req.addHeader("appId", projectId);
        // signature为之前生成的签名
        req.addHeader("signature", signature);
        req.addHeader("Content-Type", "application/json");

        // 设置HTTP请求体
        HttpEntity entity = new ByteArrayEntity(stream, ContentType
                .create(ContentType.APPLICATION_JSON.getMimeType(), "UTF-8"));
        req.setEntity(entity);

        // 执行请求
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        CloseableHttpClient cli = httpClientBuilder.build();
        HttpResponse res = cli.execute(req);
        int statusCode = res.getStatusLine().getStatusCode();
        log.debug("返回状态：" + statusCode);
        if (200 != statusCode) {
            System.out.println(statusCode);
        }
        // 获取响应
        InputStream in = res.getEntity().getContent();

        byte[] resp = readStream(in);
        String strRes = new String(resp, "UTF-8");
        log.debug("返回内容：" + strRes);
        cli.close();
        return strRes;
    }

    private String sign(byte[] stream) throws Exception {
        // 获取消息验证码类的实例，算法选择"HmacSHA256"
        Mac mac = Mac.getInstance("HmacSHA256");

        // 获取安全密钥
        Key secKey = new SecretKeySpec(
                projectSecret.getBytes("UTF-8"),
                mac.getAlgorithm());

        // 初始化
        mac.init(secKey);

        // 获得签名
        byte[] sign = mac.doFinal(stream);

        // 将byte[]格式的签名用binary编码转化为字符串返回
        return binaryEncode(sign);
    }

    public String binaryEncode(byte[] data) {
        final char hexDigits[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8',
                '9', 'a', 'b', 'c', 'd', 'e', 'f'};

        StringBuilder builder = new StringBuilder();

        for (byte i : data) {
            builder.append(hexDigits[i >>> 4 & 0xf]);
            builder.append(hexDigits[i & 0xf]);
        }

        return builder.toString();
    }

    public byte[] readStream(InputStream in) throws IOException {

        ByteArrayOutputStream output = new ByteArrayOutputStream();

        byte[] buffer = new byte[1024 * 10];
        try {

            int n = 0;
            while ((n = in.read(buffer)) != -1) {
                output.write(buffer, 0, n);
            }

            return output.toByteArray();

        } finally {
            in.close();
            output.close();
        }
    }

    public String PDFToBase64(File file) {
        BASE64Encoder encoder = new BASE64Encoder();
        FileInputStream fin = null;
        BufferedInputStream bin = null;
        ByteArrayOutputStream baos = null;
        BufferedOutputStream bout = null;
        try {
            fin = new FileInputStream(file);
            bin = new BufferedInputStream(fin);
            baos = new ByteArrayOutputStream();
            bout = new BufferedOutputStream(baos);
            byte[] buffer = new byte[1024];
            int len = bin.read(buffer);
            while (len != -1) {
                bout.write(buffer, 0, len);
                len = bin.read(buffer);
            }
            //刷新此输出流并强制写出所有缓冲的输出字节
            bout.flush();
            byte[] bytes = baos.toByteArray();
            return Base64.encode(bytes);
            //return encoder.encodeBuffer(bytes);

        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                fin.close();
                bin.close();
                bout.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 从指定 URL 下载文件并将其转换为 Base64 字符串
     *
     * @param sysFileInfo sysFileInfo
     * @return 文件的 Base64 编码
     * @throws IOException 如果网络请求或读取失败
     */
    public String getFileAsBase64(SysFileInfo sysFileInfo) throws IOException {


        // 读取文件内容到字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try (InputStream inputStream = sysFileInfoService.download(sysFileInfo)) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }

        // 转换为 Base64 编码
        byte[] fileBytes = outputStream.toByteArray();
        return  java.util.Base64.getEncoder().encodeToString(fileBytes);
    }

    /**
     * 根据oss文件地址获取文件流并响应
     * @param sysFileInfo sysFileInfo
     * @param response response
     */
    public void getFileStream(SysFileInfo sysFileInfo, HttpServletResponse response) {
        try {
            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(sysFileInfo.getFileOriginName(), "UTF-8").replace("+", "%20"));

            // 获取文件流并写入响应流
            try (InputStream inputStream = sysFileInfoService.download(sysFileInfo);
                 OutputStream outputStream = response.getOutputStream()) {
                IOUtils.copy(inputStream, outputStream);
                outputStream.flush();
            }
        } catch (Exception e) {
            throw new ServiceException(500, "文件下载失败: " + e.getMessage());
        }
    }

    /**
     * 构建 Content-Disposition 响应头，确保文件名编码安全
     */
    private static String buildContentDispositionHeader(String fileName) throws UnsupportedEncodingException {
        return "attachment;filename=" + java.net.URLEncoder.encode(fileName, "UTF-8").replace("+", "%20");
    }

    public static void main(String[] args) throws IOException {
        // 使用静态方法进行测试（仅在有实例初始化后才能使用）
        // 注意：这是一个测试方法，实际应用中应通过依赖注入使用本工具类
        System.out.println("请注意：静态方法仅在bean已初始化后可用，请在实际应用中通过依赖注入使用");

        /*
        // 下面代码仅作示例，不建议直接运行
        if (instance != null) {
            Path path = Paths.get("/Users/<USER>/Downloads/moban.pdf");
            byte[] bytes = Files.readAllBytes(path);
            PosBean posBean = new PosBean();
            posBean.setPosType("1");
            posBean.setKey("你局");
            SignPDFResult signPDFResult = getSignLocalClientStatic().localSignPDF(bytes, "33012404031138267209", 4, posBean, "");
            System.out.println(signPDFResult);
        }
        */
    }
}
