# Mysql数据库
spring:
  datasource:
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #设置严格模式,默认false不启动. 启动后在未匹配到指定数据源时候回抛出异常,不启动会使用默认数据源.
      datasource:
#        master:
#          driver-class-name: com.mysql.cj.jdbc.Driver
#          url: *********************************************************************************************************************************************************************************************************
#          username: root
#          password: hzjs@2023
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: *****************************************************************************************************************************************************************************************************
          username: root
          password: 5pJZSU72gwg17!cy4m
        dataCenter:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: *********************************************************************************************************************************************************************************************************
          username: root
          password: hzjs@2023
  redis:
    host: localhost
    port: 6379
    password:
#验证码相关配置 去除日志打印
logging:
  level:
    com.anji: off
    com.concise: debug
# oss相关配置
aliyun:
  oss:
    endpoint: oss-cn-hangzhou.aliyuncs.com
    accessKey: LTAI4FnXktcEqcLTcc9yAYk3
    secretKey: ******************************
    bucketName: lq1990
    folder: dataCollaboration
    staticDomain: https://lq1990.oss-cn-hangzhou.aliyuncs.com
    publicDomain: https://lq1990.oss-cn-hangzhou.aliyuncs.com
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
sqjzOss:
  downloadFile: false
sjxt:
  url: http://***********:7061/ebcp/services/JsfsWebService?wsdl #测试地址
  test: http://***********:7061/ebcp/services/JsfsWebService?wsdl #测试地址
  msgUrl: http://***********:7061/ebcp/services/NewMessageWebService?wsdl #测试地址
#  url: http://localhost:6099/api/services/Sqjz?wsdl #本地测试
  send: false
zwdd:
  url: http://localhost:9000/sqjzsjzx/ # 本地不发送测试
#  url: http://*************:6020/sqjzsjzx/ #可发送测试
  appKey: lI71SEqpKMKfRy3
  appSecret: NjfO5VlhEhtNvUO
  send: false
#电子签章模版字体路径
electronicSignature:
  project-id: 330001246
  project-secret: ******************************2f
  address: http://*************:9999
  domain-name: *************:9999
  modalPath: /Users/<USER>/Downloads/moban.pdf
  fontKaiPath: /Users/<USER>/Downloads/simkai.ttf
  #仿宋gbk
  fontPath: /Users/<USER>/Downloads/fs_GBK.ttf
  #宋体
  fontSongPath: /Users/<USER>/Downloads/simsun.ttf
#  modalPath: /project/dataCollaboration/code/template/moban.pdf
#  fontPath: /project/dataCollaboration/code/template/fs_GB2312.ttf
#  fontKaiPath: /project/dataCollaboration/code/template/simkai.ttf
  noticePath: /Users/<USER>/Downloads/wszz_notice.docx
  bl_bgr: /Users/<USER>/Downloads/测试笔录/bl_bgr.docx
  bl_bhr: /Users/<USER>/Downloads/测试笔录/bl_bhr.docx
  bl_csgb: /Users/<USER>/Downloads/bl_csgb.docx
  bl_fjsbzrhjs: /Users/<USER>/Downloads/bl_fjsbzrhjs.docx
  bl_jsbzrhjs: /Users/<USER>/Downloads/bl_jsbzrhjs.docx
  xzhyPath: /Users/<USER>/Downloads/wszz_xzhy.docx
  jtpyPath: /Users/<USER>/Downloads/wszz_jtpy.docx
  dcpgyjsPath: /Users/<USER>/Downloads/wszz_dcpgyjs.docx
  dcpgbPath: /Users/<USER>/Downloads/wszz_dcpgb.docx
  fmPath: /Users/<USER>/Downloads/fm.pdf
  sqsPath: /Users/<USER>/Downloads/sqs.docx
tessDataPath: /Users/<USER>/Downloads/11/tesseract
# python工具
pythonTool:
  pdfToWord: http://************:6046/convert
jodconverter:
  local:
    #暂时关闭预览，启动时会有点慢
    enabled: true
    #设置libreoffice主目录 linux地址如：/usr/lib64/libreoffice
    office-home: /Applications/LibreOffice.app/Contents
#    office-home: /usr/lib64/libreoffice
    kill-existing-process: true
