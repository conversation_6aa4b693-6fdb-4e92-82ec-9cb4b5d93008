package com.concise.modular.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.DeviceSignLog;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.PdfSignatureParams;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.common.util.PdfSignUtil;
import com.concise.core.context.login.LoginContextHolder;
import com.concise.core.login.SysLoginUser;
import com.concise.gen.investigation.param.InvestigationTranscriptParam;
import com.concise.gen.investigationotheruser.service.InvestigationOtherUserService;
import com.concise.gen.investigationsign.entity.InvestigationSign;
import com.concise.gen.investigationsign.param.InvestigationSignParam;
import com.concise.gen.investigationsign.service.InvestigationSignService;
import com.concise.sys.modular.org.entity.SysOrg;
import com.concise.sys.modular.org.service.SysOrgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 调查评估_手签控制器
 *
 * <AUTHOR>
 * @date 2025-04-21 20:30:30
 */
@Slf4j
@Api(tags = "调查评估_手签")
@RestController
public class InvestigationSignController {


    @Resource
    private InvestigationSignService investigationSignService;

    @Resource
    private SysFileInfoService sysFileInfoService;

    @Resource
    private SysOrgService sysOrgService;

    @Resource
    private InvestigationOtherUserService investigationOtherUserService;

    /**
     * 查询调查评估_手签
     *
     * <AUTHOR>
     * @date 2025-04-21 20:30:30
     */
    @GetMapping("/investigationSign/page")
    @ApiOperation("调查评估_手签_分页查询")
    public ResponseData page(InvestigationSignParam investigationSignParam) {
        return new SuccessResponseData(investigationSignService.page(investigationSignParam));
    }

    /**
     * 添加调查评估_手签
     *
     * <AUTHOR>
     * @date 2025-04-21 20:30:30
     */
    @PostMapping("/investigationSign/add")
    @ApiOperation("调查评估_手签_增加")
    @BusinessLog(title = "调查评估_手签_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(InvestigationSignParam.add.class) InvestigationSignParam investigationSignParam) {
        investigationSignService.add(investigationSignParam);
        return new SuccessResponseData();
    }

    /**
     * 调查评估_手签_新增浙政钉签名用户
     *
     * @param investigationSignParamList
     * @return
     * @deprecated 请使用新的统一接口 {@link #addSignUsers(JSONObject)} 代替
     */
    @Deprecated
    @PostMapping("/investigationSign/addZzDingUser")
    @ApiOperation(value = "调查评估_手签_新增浙政钉签名用户", notes = "此接口已过时，请使用/investigationSign/addSignUsers替代")
    @BusinessLog(title = "调查评估_手签_新增浙政钉签名用户", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData addZzDingUser(@RequestBody List<InvestigationSignParam> investigationSignParamList) {
        if (ObjectUtil.isEmpty(investigationSignParamList)) {
            return ResponseData.error("接收人不能为空");
        }
        for (InvestigationSignParam investigationSignParam : investigationSignParamList) {
            SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUser();
            investigationSignParam.setSendOrgId(sysLoginUser.getLoginEmpInfo().getOrgId());
            investigationSignParam.setSendOrgName(sysLoginUser.getLoginEmpInfo().getOrgName());

            //防止重复
            int count = investigationSignService.count(new QueryWrapper<InvestigationSign>().lambda().eq(InvestigationSign::getPid, investigationSignParam.getPid())
                    .eq(InvestigationSign::getPaperId, investigationSignParam.getPaperId())
                    .eq(InvestigationSign::getReceiveUserId, investigationSignParam.getReceiveUserId()));
            if (count > 0) {
                return ResponseData.error("该用户数据已存在，请勿重复！");
            }

        }
        investigationSignService.addZzDingUser(investigationSignParamList);
        return new SuccessResponseData();
    }

    /**
     * 调查评估_手签_新增浙里办签名用户
     *
     * @param investigationSignParamList
     * @return
     * @deprecated 请使用新的统一接口 {@link #addSignUsers(JSONObject)} 代替
     */
    @Deprecated
    @PostMapping("/investigationSign/addZlBUser")
    @ApiOperation(value = "调查评估_手签_新增浙里办签名用户", notes = "此接口已过时，请使用/investigationSign/addSignUsers替代")
    @BusinessLog(title = "调查评估_手签_新增浙里办签名用户", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData addZlBUser(@RequestBody List<InvestigationSignParam> investigationSignParamList) {
        if (ObjectUtil.isEmpty(investigationSignParamList)) {
            return ResponseData.error("接收人不能为空");
        }
        for (InvestigationSignParam investigationSignParam : investigationSignParamList) {
            if (ObjectUtil.isEmpty(investigationSignParam.getPhone())) {
                return ResponseData.error("接收人手机号不能为空");
            }
            SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUser();
            investigationSignParam.setSendOrgId(sysLoginUser.getLoginEmpInfo().getOrgId());
            investigationSignParam.setSendOrgName(sysLoginUser.getLoginEmpInfo().getOrgName());
            //防止重复
            int count = investigationSignService.count(new QueryWrapper<InvestigationSign>().lambda().eq(InvestigationSign::getPid, investigationSignParam.getPid())
                    .eq(InvestigationSign::getPaperId, investigationSignParam.getPaperId())
                    .eq(InvestigationSign::getPhone, investigationSignParam.getPhone()));
            if (count > 0) {
                return ResponseData.error("该用户数据已存在，请勿重复！");
            }
        }
        investigationSignService.addZlBUser(investigationSignParamList);
        return new SuccessResponseData();
    }

    /**
     * 调查评估_手签_统一添加浙政钉和浙里办签名用户
     *
     * @param jsonObject 包含浙政钉和浙里办用户的JSON对象
     * @return 操作结果
     */
    @PostMapping("/investigationSign/addSignUsers")
    @ApiOperation(value = "调查评估_手签_统一添加浙政钉和浙里办签名用户", notes = "接收一个JSON对象，包含zzdUsers和zlbUsers两个数组，分别代表浙政钉用户和浙里办用户列表")
    @BusinessLog(title = "调查评估_手签_统一添加浙政钉和浙里办签名用户", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData addSignUsers(@RequestBody JSONObject jsonObject) {
        if (jsonObject == null) {
            return ResponseData.error("请求参数不能为空");
        }

        // 获取当前登录用户信息
        SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUser();
        String orgId1 = sysLoginUser.getLoginEmpInfo().getOrgId();
        String orgId = sysLoginUser.getLoginEmpInfo().getOrgId();
        String orgName = sysLoginUser.getLoginEmpInfo().getOrgName();
        if (ObjectUtil.isNotEmpty(orgId1)) {
            SysOrg sysOrg = sysOrgService.getById(orgId1);
            if (ObjectUtil.isNotEmpty(sysOrg) && ObjectUtil.isNotEmpty(sysOrg.getType()) && "sp".equals(sysOrg.getType())) {
                SysOrg porg = sysOrgService.getById(sysOrg.getPid());
                orgId = porg.getId();
                orgName = porg.getOrgName();
            }
        }

        // 获取浙政钉用户列表
        List<InvestigationSignParam> zzdUserList = new ArrayList<>();
        String paperId = null; // 用于存储文书ID，供后续查询已有记录使用
        String type = jsonObject.getString("type");
        if (jsonObject.containsKey("zzdUsers")) {
            JSONArray zzdUsersArray = jsonObject.getJSONArray("zzdUsers");
            if (zzdUsersArray != null && zzdUsersArray.size() > 0) {
                for (int i = 0; i < zzdUsersArray.size(); i++) {
                    JSONObject userObj = zzdUsersArray.getJSONObject(i);
                    InvestigationSignParam param = userObj.toJavaObject(InvestigationSignParam.class);

                    // 基础校验
                    if (ObjectUtil.isEmpty(param.getPid())) {
                        return ResponseData.error("浙政钉用户缺少必要参数：调查评估ID");
                    }
                    if (ObjectUtil.isEmpty(param.getPaperId())) {
                        return ResponseData.error("浙政钉用户缺少必要参数：文书ID");
                    }
                    if (paperId == null) {
                        paperId = param.getPaperId(); // 获取文书ID用于后续查询
                    } else if (!paperId.equals(param.getPaperId())) {
                        return ResponseData.error("所有用户必须使用相同的文书ID");
                    }
                    if (ObjectUtil.isEmpty(param.getReceiveUserId())) {
                        return ResponseData.error("浙政钉用户缺少必要参数：用户ID");
                    }
                    if (ObjectUtil.isEmpty(param.getReceiveUserName())) {
                        return ResponseData.error("浙政钉用户缺少必要参数：用户姓名");
                    }
                    if (ObjectUtil.isEmpty(param.getTitle())) {
                        return ResponseData.error("浙政钉用户缺少必要参数：文书标题");
                    }
//                    if (ObjectUtil.isEmpty(param.getPaperType())) {
//                        return ResponseData.error("浙政钉用户缺少必要参数：文书类型");
//                    }


                    // 设置发送单位信息
                    param.setSendOrgId(orgId);
                    param.setSendOrgName(orgName);
                    param.setReceiveUserType(1); // 设置为浙政钉用户类型
                    param.setType(type);//手动上传标志
                    // 检查重复（在当前列表中）
                    boolean isDuplicate = false;
                    for (InvestigationSignParam existingParam : zzdUserList) {
                        if (existingParam.getReceiveUserId().equals(param.getReceiveUserId())) {
                            isDuplicate = true;
                            break;
                        }
                    }
                    if (isDuplicate) {
                        return ResponseData.error("浙政钉用户 " + param.getReceiveUserName() + " 在请求中重复");
                    }

                    zzdUserList.add(param);
                }
            }
        }

        // 获取浙里办用户列表
        List<InvestigationSignParam> zlbUserList = new ArrayList<>();
        if (jsonObject.containsKey("zlbUsers")) {
            JSONArray zlbUsersArray = jsonObject.getJSONArray("zlbUsers");
            if (zlbUsersArray != null && zlbUsersArray.size() > 0) {
                for (int i = 0; i < zlbUsersArray.size(); i++) {
                    JSONObject userObj = zlbUsersArray.getJSONObject(i);
                    InvestigationSignParam param = userObj.toJavaObject(InvestigationSignParam.class);

                    // 基础校验
                    if (ObjectUtil.isEmpty(param.getPid())) {
                        return ResponseData.error("浙里办用户缺少必要参数：调查评估ID");
                    }
                    if (ObjectUtil.isEmpty(param.getPaperId())) {
                        return ResponseData.error("浙里办用户缺少必要参数：文书ID");
                    }
                    if (paperId == null) {
                        paperId = param.getPaperId(); // 获取文书ID用于后续查询
                    } else if (!paperId.equals(param.getPaperId())) {
                        return ResponseData.error("所有用户必须使用相同的文书ID");
                    }
                    if (ObjectUtil.isEmpty(param.getReceiveUserName())) {
                        return ResponseData.error("浙里办用户缺少必要参数：用户姓名");
                    }
                    if (ObjectUtil.isEmpty(param.getPhone())) {
                        return ResponseData.error("浙里办用户缺少必要参数：手机号");
                    }
                    if (ObjectUtil.isEmpty(param.getTitle())) {
                        return ResponseData.error("浙里办用户缺少必要参数：文书标题");
                    }


                    // 设置发送单位信息
                    param.setSendOrgId(orgId);
                    param.setSendOrgName(orgName);
                    param.setReceiveUserType(2); // 设置为浙里办用户类型
                    param.setType(type);//手动上传标志
                    // 检查重复（在当前列表中）
                    boolean isDuplicate = false;
                    for (InvestigationSignParam existingParam : zlbUserList) {
                        if (existingParam.getPhone().equals(param.getPhone())) {
                            isDuplicate = true;
                            break;
                        }
                    }
                    if (isDuplicate) {
                        return ResponseData.error("浙里办用户 " + param.getReceiveUserName() + " 在请求中重复");
                    }

                    zlbUserList.add(param);
                }
            }
        }


        // 确保文书ID存在
        if (paperId == null) {
            return ResponseData.error("缺少必要参数：文书ID");
        }

        try {
            // 处理浙政钉用户
            if (!zzdUserList.isEmpty()) {
                // 1. 查询已有的浙政钉用户记录
                List<InvestigationSign> existingZzdList = investigationSignService.list(
                        new QueryWrapper<InvestigationSign>().lambda()
                                .eq(InvestigationSign::getPaperId, paperId)
                                .eq(InvestigationSign::getReceiveUserType, 1));

                // 2. 处理浙政钉用户：找出需要添加的用户
                List<InvestigationSignParam> zzdToAdd = new ArrayList<>();
                for (InvestigationSignParam newParam : zzdUserList) {
                    boolean exists = false;
                    for (InvestigationSign existingSign : existingZzdList) {
                        if (existingSign.getReceiveUserId().equals(newParam.getReceiveUserId())
                                && existingSign.getPid().equals(newParam.getPid())
                                && existingSign.getPaperId().equals(newParam.getPaperId())) {
                            exists = true;
                            break;
                        }
                    }
                    if (!exists) {
                        zzdToAdd.add(newParam);
                    }
                }

                // 3. 找出需要删除的浙政钉用户
                List<InvestigationSign> zzdToDelete = new ArrayList<>();
                for (InvestigationSign existingSign : existingZzdList) {
                    boolean exists = false;
                    for (InvestigationSignParam newParam : zzdUserList) {
                        if (existingSign.getReceiveUserId().equals(newParam.getReceiveUserId())
                                && existingSign.getPid().equals(newParam.getPid())
                                && existingSign.getPaperId().equals(newParam.getPaperId())) {
                            exists = true;
                            break;
                        }
                    }
                    // 只删除状态为"0"（待签）的记录
                    if (!exists && "0".equals(existingSign.getStatus())) {
                        zzdToDelete.add(existingSign);
                    }
                }

                // 4. 删除不在新列表中的浙政钉用户（仅待签状态）
                for (InvestigationSign sign : zzdToDelete) {
                    log.debug("删除浙政钉用户：文书ID={}，用户ID={}", sign.getPaperId(), sign.getReceiveUserId());
                    investigationSignService.remove(new QueryWrapper<InvestigationSign>().lambda()
                            .eq(InvestigationSign::getPid, sign.getPid())
                            .eq(InvestigationSign::getPaperId, sign.getPaperId())
                            .eq(InvestigationSign::getReceiveUserId, sign.getReceiveUserId())
                            .eq(InvestigationSign::getStatus, "0"));
                }

                // 5. 添加新的浙政钉用户
                if (!zzdToAdd.isEmpty()) {
                    log.debug("添加新的浙政钉用户，文书ID：{}，用户数量：{}", paperId, zzdToAdd.size());
                    investigationSignService.addZzDingUser(zzdToAdd);
                }
            } else {
                // 浙政钉用户列表为空，删除该文书下所有浙政钉待签状态的记录
                log.debug("浙政钉用户列表为空，清除文书ID：{} 下所有浙政钉待签状态的记录", paperId);
                boolean removed = investigationSignService.remove(new QueryWrapper<InvestigationSign>().lambda()
                        .eq(InvestigationSign::getPaperId, paperId)
                        .eq(InvestigationSign::getReceiveUserType, 1)
                        .eq(InvestigationSign::getStatus, "0"));
                log.debug("删除浙政钉待签用户完成：{}", removed ? "成功" : "无记录或失败");
            }

            // 处理浙里办用户
            if (!zlbUserList.isEmpty()) {
                // 1. 查询已有的浙里办用户记录
                List<InvestigationSign> existingZlbList = investigationSignService.list(
                        new QueryWrapper<InvestigationSign>().lambda()
                                .eq(InvestigationSign::getPaperId, paperId)
                                .eq(InvestigationSign::getReceiveUserType, 2));

                // 2. 处理浙里办用户：找出需要添加的用户
                List<InvestigationSignParam> zlbToAdd = new ArrayList<>();
                for (InvestigationSignParam newParam : zlbUserList) {
                    boolean exists = false;
                    for (InvestigationSign existingSign : existingZlbList) {
                        if (existingSign.getPhone().equals(newParam.getPhone())
                                && existingSign.getPid().equals(newParam.getPid())
                                && existingSign.getPaperId().equals(newParam.getPaperId())) {
                            exists = true;
                            break;
                        }
                    }
                    if (!exists) {
                        zlbToAdd.add(newParam);
                    }
                }

                // 3. 找出需要删除的浙里办用户
                List<InvestigationSign> zlbToDelete = new ArrayList<>();
                for (InvestigationSign existingSign : existingZlbList) {
                    boolean exists = false;
                    for (InvestigationSignParam newParam : zlbUserList) {
                        if (existingSign.getPhone().equals(newParam.getPhone())
                                && existingSign.getPid().equals(newParam.getPid())
                                && existingSign.getPaperId().equals(newParam.getPaperId())) {
                            exists = true;
                            break;
                        }
                    }
                    // 只删除状态为"0"（待签）的记录
                    if (!exists && "0".equals(existingSign.getStatus())) {
                        zlbToDelete.add(existingSign);
                    }
                }

                // 4. 删除不在新列表中的浙里办用户（仅待签状态）
                for (InvestigationSign sign : zlbToDelete) {
                    log.debug("删除浙里办用户：文书ID={}，手机号={}", sign.getPaperId(), sign.getPhone());
                    investigationSignService.remove(new QueryWrapper<InvestigationSign>().lambda()
                            .eq(InvestigationSign::getPid, sign.getPid())
                            .eq(InvestigationSign::getPaperId, sign.getPaperId())
                            .eq(InvestigationSign::getPhone, sign.getPhone())
                            .eq(InvestigationSign::getStatus, "0"));
                }

                // 5. 添加新的浙里办用户
                if (!zlbToAdd.isEmpty()) {
                    log.debug("添加新的浙里办用户，文书ID：{}，用户数量：{}", paperId, zlbToAdd.size());
                    investigationSignService.addZlBUser(zlbToAdd);
                }
            } else {
                // 浙里办用户列表为空，删除该文书下所有浙里办待签状态的记录
                log.debug("浙里办用户列表为空，清除文书ID：{} 下所有浙里办待签状态的记录", paperId);
                boolean removed = investigationSignService.remove(new QueryWrapper<InvestigationSign>().lambda()
                        .eq(InvestigationSign::getPaperId, paperId)
                        .eq(InvestigationSign::getReceiveUserType, 2)
                        .eq(InvestigationSign::getStatus, "0"));
                log.debug("删除浙里办待签用户完成：{}", removed ? "成功" : "无记录或失败");
            }

            // 再次查询最新数据，用于统计
            int zzdRemain = investigationSignService.count(new QueryWrapper<InvestigationSign>().lambda()
                    .eq(InvestigationSign::getPaperId, paperId)
                    .eq(InvestigationSign::getReceiveUserType, 1));
            int zlbRemain = investigationSignService.count(new QueryWrapper<InvestigationSign>().lambda()
                    .eq(InvestigationSign::getPaperId, paperId)
                    .eq(InvestigationSign::getReceiveUserType, 2));

            investigationOtherUserService.addOtherUser(zlbUserList);

            // 构建响应信息
            Map<String, Object> resultInfo = new HashMap<>();
            resultInfo.put("paperId", paperId);
            resultInfo.put("zzdTotal", zzdRemain);
            resultInfo.put("zlbTotal", zlbRemain);

            StringBuilder message = new StringBuilder();
            message.append("处理完成，");
            message.append("浙政钉用户共 ").append(zzdRemain).append(" 个，");
            message.append("浙里办用户共 ").append(zlbRemain).append(" 个。");

            resultInfo.put("message", message.toString());

            return new SuccessResponseData(resultInfo);
        } catch (Exception e) {
            log.error("处理用户数据失败", e);
            return ResponseData.error("处理用户数据失败：" + e.getMessage());
        }
    }

    /**
     * 调查评估_手签_根据文件id分别查询浙政钉和浙里办用户
     *
     * @param paperId
     * @return
     */
    @GetMapping("/investigationSign/getByPaperId")
    @ApiOperation("调查评估_手签_根据文件id分别查询浙政钉和浙里办用户")
    public ResponseData getByPaperId(@RequestParam String paperId) {
        SysFileInfo sysFileInfo = sysFileInfoService.getById(paperId);
        if (ObjectUtil.isEmpty(sysFileInfo)) {
            return ResponseData.error("文件不存在");
        }
        List<InvestigationSign> zzdList = investigationSignService.list(new QueryWrapper<InvestigationSign>().lambda().eq(InvestigationSign::getPaperId, paperId)
                .eq(InvestigationSign::getReceiveUserType, 1));
        List<InvestigationSign> zlBList = investigationSignService.list(new QueryWrapper<InvestigationSign>().lambda().eq(InvestigationSign::getPaperId, paperId)
                .eq(InvestigationSign::getReceiveUserType, 2));
        Map<String, Object> result = new HashMap<>();
        result.put("zzdUsers", zzdList);
        result.put("zlbUsers", zlBList);
        return new SuccessResponseData(result);
    }

    /**
     * 调查评估_手签_根据文件id查询所有已推送的用户
     */
    @GetMapping("/investigationSign/getAllByPaperId")
    @ApiOperation("调查评估_手签_根据文件id查询所有已推送的用户")
    public ResponseData getAllByPaperId(@RequestParam String paperId) {
        List<InvestigationSign> list = investigationSignService.list(new QueryWrapper<InvestigationSign>().lambda().eq(InvestigationSign::getPaperId, paperId));
        return new SuccessResponseData(list);
    }

    /**
     * 删除调查评估_手签
     *
     * <AUTHOR>
     * @date 2025-04-21 20:30:30
     */
    @PostMapping("/investigationSign/delete")
    @ApiOperation("调查评估_手签_删除")
    @BusinessLog(title = "调查评估_手签_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(InvestigationSignParam.delete.class) InvestigationSignParam investigationSignParam) {
        investigationSignService.delete(investigationSignParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑调查评估_手签
     *
     * <AUTHOR>
     * @date 2025-04-21 20:30:30
     */
    @PostMapping("/investigationSign/edit")
    @ApiOperation("调查评估_手签_编辑")
    @BusinessLog(title = "调查评估_手签_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(InvestigationSignParam.edit.class) InvestigationSignParam investigationSignParam) {
        investigationSignService.edit(investigationSignParam);
        return new SuccessResponseData();
    }

    /**
     * 查看调查评估_手签
     *
     * <AUTHOR>
     * @date 2025-04-21 20:30:30
     */
    @GetMapping("/investigationSign/detail")
    @ApiOperation("调查评估_手签_查看")
    public ResponseData detail(@Validated(InvestigationSignParam.detail.class) InvestigationSignParam investigationSignParam) {
        return new SuccessResponseData(investigationSignService.detail(investigationSignParam));
    }

    /**
     * 调查评估_手签列表
     *
     * <AUTHOR>
     * @date 2025-04-21 20:30:30
     */
    @GetMapping("/investigationSign/list")
    @ApiOperation("调查评估_手签_列表")
    public ResponseData list(InvestigationSignParam investigationSignParam) {
        return new SuccessResponseData(investigationSignService.list(investigationSignParam));
    }

    /**
     * 在PDF中查找指定文本位置
     *
     * @param params 请求参数
     * @return 文本位置信息
     */
    @PostMapping("/investigationSign/findTextLocations")
    @ApiOperation("查找PDF中的文本位置")
    public ResponseData findTextLocations(@RequestBody PdfSignatureParams params) {
        return investigationSignService.findTextLocations(params);
    }


    /**
     * 在PDF中查找指定文本后添加多个图片并自动排列
     *
     * @param params   请求参数
     * @param response HTTP响应对象
     */
    @PostMapping("/investigationSign/addImages")
    @ApiOperation("在PDF指定关键字后添加多个图片并自动排列")
    public void addImages(
            @RequestBody PdfSignatureParams params,
            HttpServletResponse response) {
        investigationSignService.addImages(params, response);
    }


    /**
     * 对某个PDF文件进行批量签名
     *
     * @param paperId 文书ID
     * @return 签名后的PDF文件字节数组
     */
    @GetMapping("/investigationSign/batchSign")
    @ApiOperation("调查评估_手签_批量签名")
    public void batchSign(@RequestParam String paperId, HttpServletResponse response) {
        investigationSignService.batchSign(paperId, response);
    }


    /**
     * 获取签名列表
     *
     * @param paperId 文书id
     * @return
     */
    @GetMapping("/investigationSign/signList")
    @ApiOperation("获取签名列表")
    public ResponseData signList(@RequestParam String paperId) {
        LambdaQueryWrapper<InvestigationSign> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InvestigationSign::getPaperId, paperId);
        lambdaQueryWrapper.eq(InvestigationSign::getStatus, "1");
        lambdaQueryWrapper.orderByDesc(InvestigationSign::getSendTime);
        List<InvestigationSign> investigationSigns = investigationSignService.list(lambdaQueryWrapper);
        List<InvestigationSign> newSignList = investigationSigns.stream().map(investigationSign -> {
                    if (StringUtils.isNotBlank(investigationSign.getSignFileBase64())) {
                        String base64String = investigationSign.getSignFileBase64();
                        if (base64String.contains(",")) {
                            base64String = base64String.substring(base64String.indexOf(",") + 1);
                        }
                        // 移除可能存在的换行符、空格等
                        base64String = base64String.replaceAll("\\s", "");
                        byte[] bytes = Base64.getDecoder().decode(base64String);
                        bytes = rotateLeftImage(bytes);
                        if (bytes != null) {
                            investigationSign.setSignFileBase64("data:image/png;base64," + Base64.getEncoder().encodeToString(bytes));
                        }
                    }
                    return investigationSign;
                }
        ).collect(Collectors.toList());
        return ResponseData.success(newSignList);
    }


    /**
     * 获取最新签名
     *
     * @return
     */
    @GetMapping("/investigationSign/lastSign")
    @ApiOperation("获取最新签名")
    public ResponseData lastSign(String id, String bizType, String fileId) {
        SysFileInfo sysFileInfo = sysFileInfoService.getById(fileId);
        if (sysFileInfo == null) {
            return ResponseData.success();
        }
        LambdaQueryWrapper<InvestigationSign> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(InvestigationSign::getPid, id);
        lambdaQueryWrapper.eq(InvestigationSign::getStatus, "1");
        //lambdaQueryWrapper.eq(InvestigationSign::getPaperId,fileId);
        lambdaQueryWrapper.eq(InvestigationSign::getPaperType, bizType);
        lambdaQueryWrapper.ge(InvestigationSign::getSendTime, sysFileInfo.getCreateTime());
        lambdaQueryWrapper.orderByDesc(InvestigationSign::getSendTime);
        List<InvestigationSign> investigationSigns = investigationSignService.list(lambdaQueryWrapper);
        List<InvestigationSign> newSignList = investigationSigns.stream().map(investigationSign -> {
                    if (StringUtils.isNotBlank(investigationSign.getSignFileBase64())) {
                        String base64String = investigationSign.getSignFileBase64();
                        if (base64String.contains(",")) {
                            base64String = base64String.substring(base64String.indexOf(",") + 1);
                        }
                        // 移除可能存在的换行符、空格等
                        base64String = base64String.replaceAll("\\s", "");
                        byte[] bytes = Base64.getDecoder().decode(base64String);
                        bytes = rotateLeftImage(bytes);
                        if (bytes != null) {
                            investigationSign.setSignFileBase64("data:image/png;base64," + Base64.getEncoder().encodeToString(bytes));
                        }
                    }
                    return investigationSign;
                }
        ).collect(Collectors.toList());
        return ResponseData.success(newSignList);
    }

    /**
     * 旋转图片(逆时针90度)
     *
     * @param imageBytes 原始图片字节数组
     * @return 旋转后的图片字节数组
     */
    private static byte[] rotateLeftImage(byte[] imageBytes) {
        try {

            ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes);
            BufferedImage srcImage = ImageIO.read(bis);
            bis.close();

            if (srcImage == null) {
                log.error("无法读取图片数据");
                return imageBytes; // 返回原始图片
            }

            // 计算旋转后的图片尺寸
            int width = srcImage.getWidth();
            int height = srcImage.getHeight();

            // 向右旋转90度（逆时针）
            BufferedImage rotatedImage = new BufferedImage(height, width, srcImage.getType());
            for (int x = 0; x < width; x++) {
                for (int y = 0; y < height; y++) {
                    // 顺时针旋转90度: (x,y) -> (y, width-1-x)
                    rotatedImage.setRGB(y, width - x - 1, srcImage.getRGB(x, y));
                }
            }

            // 将旋转后的图片转换为字节数组
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ImageIO.write(rotatedImage, "PNG", bos);
            byte[] rotatedImageBytes = bos.toByteArray();
            bos.close();

            return rotatedImageBytes;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 最新已签名文书检测接口
     *
     * @param id         调查评估的id
     * @param fileId     附件id
     * @param bizType    附件类型
     * @param fileBucket bizType是 BLLX 时 该字段存笔录类型的字典 如 BLLX01、BLLX02...
     * @param blId       笔录id
     * @return
     */
    @GetMapping("/investigationSign/latestSigned")
    @ApiOperation("最新已签名文书检测接口")
    public ResponseData latestSigned(@RequestParam String id, @RequestParam String fileId, @RequestParam String bizType, @RequestParam(required = false) String fileBucket, @RequestParam(required = false) String blId) {
        Map<String, Object> map = new HashMap<>();
        boolean hasNewSign = false;
        SysFileInfo sysFileInfo = null;
        List<InvestigationSign> list = null;
        if (!"BLLX".equals(bizType)) {
            list = investigationSignService.list(new QueryWrapper<InvestigationSign>().lambda().eq(InvestigationSign::getPid, id).eq(InvestigationSign::getPaperType, bizType));
        } else {
            //因为笔录bizType会重复，所以根据blId来查找，确保是同一份笔录文书
            list = investigationSignService.list(new QueryWrapper<InvestigationSign>().lambda().eq(InvestigationSign::getPid, id).eq(InvestigationSign::getPaperType, bizType)
                    .eq(InvestigationSign::getBlId, blId));
        }
        if (CollectionUtil.isNotEmpty(list)) {
            for (InvestigationSign investigationSign : list) {
                if (ObjectUtil.isNotEmpty(investigationSign.getPaperId())) {
                    if (!investigationSign.getPaperId().equals(fileId)) {
                        hasNewSign = true;
                        sysFileInfo = sysFileInfoService.getById(investigationSign.getPaperId());
                        if (investigationSign.getType() != null) {
                            sysFileInfo.setType(investigationSign.getType());
                        }
                    }
                    break;
                }
            }
            if (ObjectUtil.isNull(sysFileInfo)) {
                sysFileInfo = sysFileInfoService.getById(fileId);
            }
        }
        map.put("hasNewSign", hasNewSign);
        map.put("fileInfo", sysFileInfo);
        //防止意外，拿到之后再对比一下
        if (ObjectUtil.isNotEmpty(sysFileInfo)) {
            if (sysFileInfo.getId().toString().equals(fileId)) {
                map.put("hasNewSign", false);
            }
        }
        return new SuccessResponseData(map);
    }

    /**
     * 重新制作文书时，删除前面已发送的多余文件
     */
    @DeleteMapping("/investigationSign/deleteOld")
    @ApiOperation("重新制作文书时，删除前面已发送的多余文件")
    @BusinessLog(title = "重新制作文书时，删除前面已发送的多余文件", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData deleteOld(@RequestParam String id, @RequestParam String bizType) {
        investigationSignService.deleteOld(id, bizType);
        return new SuccessResponseData();
    }

    /**
     * 根据位置对pdf进行签名，授权签
     */
    @GetMapping("/investigationSign/signByPosition")
    @ApiOperation("根据位置对pdf进行签名，授权签")
    @BusinessLog(title = "根据位置对pdf进行签名，授权签", opType = LogAnnotionOpTypeEnum.UPDATE)
    public ResponseData signByPosition(@RequestParam String id, @RequestParam float x, @RequestParam float y, @RequestParam Integer pageNo, @RequestParam String signId, @RequestParam String bizType, @RequestParam String infoId, String type) {
        SysFileInfo sysFileInfo = investigationSignService.signByPosition(id, x, y, pageNo, signId, bizType, infoId, type);
        return new SuccessResponseData(sysFileInfo);
    }


    /**
     * 指定坐标签名
     */
    @PostMapping("/investigationSign/signByDrag")
    @ApiOperation("指定坐标签名")
    @BusinessLog(title = "指定坐标签名", opType = LogAnnotionOpTypeEnum.UPDATE)
    public ResponseData signByDrag(@RequestBody JSONObject param) {
        SysFileInfo sysFileInfo = investigationSignService.signByDrag(param.getString("id"),
                param.getFloat("x"),
                param.getFloat("y"),
                param.getFloat("signFileWidth"),
                param.getFloat("signFileHeight"),
                param.getInteger("pageNo"),
                param.getString("signFileBase64"));
        //更新签名绑定新的文书id
        LambdaUpdateWrapper<InvestigationSign> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(InvestigationSign::getPaperId, param.getString("fileId"));
        lambdaUpdateWrapper.set(InvestigationSign::getPaperId, sysFileInfo.getId());
        investigationSignService.update(lambdaUpdateWrapper);
        return new SuccessResponseData(sysFileInfo);
    }


    @PostMapping("/investigationSign/signByDevice")
    @ApiOperation("设备签名捺印")
    @DeviceSignLog()
    public ResponseData signByDevice(@RequestBody JSONObject param) {
        return new SuccessResponseData(investigationSignService.signByDevice(
                param.getString("id"),
                param.getString("bizType"),
                param.getString("infoId"),
                param.getString("imgBase64Signature"),
                param.getString("imgBase64Fingerprint")
        )
        );
    }

    @PostMapping("/test/investigationSign/signByDeviceLog")
    public ResponseData signByDeviceLog(@RequestBody JSONObject param) {
        try {
            File file = new File("/Users/<USER>/Downloads/11/1934870733561794562.pdf");
            // 创建文件输入流
            FileInputStream fileInputStream = new FileInputStream(file);
            // 创建字节数组
            byte[] bytes = new byte[(int) file.length()];
            // 读取文件内容到字节数组
            fileInputStream.read(bytes);
            // 关闭输入流
            fileInputStream.close();
            // 将字节数组转换为Base64字符串
            String base64 = Base64.getEncoder().encodeToString(bytes);

            byte[] pdf = investigationSignService.getSinglePointAllDeviceSignedLog(base64, "1934789825820332034", "eval_deliberate", 0.0f);
            PdfSignUtil.savePdfToFile(pdf, "/Users/<USER>/Downloads/11/test.pdf");
        } catch (Exception e) {

        }

        return new SuccessResponseData();
    }

    /**
     * 接收已签名通知
     */
    @GetMapping("/investigationSign/receiveSigned")
    @ApiOperation("接收已签名通知")
    public ResponseData receiveSigned(@RequestParam String id) {
        InvestigationSign investigationSign = investigationSignService.getById(id);
        if (ObjectUtil.isEmpty(investigationSign)) {
            return ResponseData.error("签名记录不存在");
        }
        investigationSignService.signed(investigationSign);
        return new SuccessResponseData();
    }

    /**
     * 检测当前环节是否有待签名的
     */
    @GetMapping("/investigationSign/check")
    @ApiOperation("检测当前环节是否有待签名的")
    public ResponseData check(@RequestParam String id, @RequestParam String bizType) {
        return investigationSignService.check(id, bizType);
    }

    /**
     * 测试按顺序签
     */
    @GetMapping("/investigationSign/test")
    @ApiOperation("测试按顺序签")
    public void test(@RequestParam String fileId, @RequestParam String signId, @RequestParam Integer sort, HttpServletResponse response) {
        try {
            byte[] pdfBytes = investigationSignService.test(fileId, signId, sort);

            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "inline; filename=signed.pdf");

            response.getOutputStream().write(pdfBytes);
            response.flushBuffer();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 用于ccgf调用，查看签名效果
     */
    @PostMapping("/investigationSign/preSign")
    @ApiOperation("用于ccgf调用，查看签名效果")
    public ResponseData preSign(@RequestBody InvestigationSign investigationSign) {
        return new SuccessResponseData(investigationSignService.preSign(investigationSign.getId(), investigationSign.getSignFileBase64()));
    }

    /**
     * 用于ccgf调用，查看签名效果（用于笔录）
     */
    @PostMapping("/investigationSign/preSignBl")
    @ApiOperation("用于ccgf调用，查看签名效果")
    @BusinessLog(title = "调查评估_移动端笔录查看签名效果", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData preSignBl(@RequestBody InvestigationSign investigationSign) {
        return new SuccessResponseData(investigationSignService.preSignBl(investigationSign.getId(), investigationSign.getSignFileBase64()));
    }

    /**
     * 用于ccgf调用，笔录确认后提交
     */
    @PostMapping("/investigationSign/subSign")
    @ApiOperation("用于ccgf调用，笔录确认后提交")
    @BusinessLog(title = "调查评估_移动端笔录确认后提交", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData subSign(@RequestBody InvestigationTranscriptParam investigationTranscriptParam) {
        return new SuccessResponseData(investigationSignService.subSign(investigationTranscriptParam));
    }
}

